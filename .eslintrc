{"parser": "babel-es<PERSON>", "env": {"browser": true, "node": true}, "globals": {"process": false, "require": false, "define": false, "console": false, "__UPLOAD_API__": false, "__API_HOST__": false, "__INVITE_KEY__": false, "__LATEST_TERMS__": false}, "extends": ["plugin:react-hooks/recommended"], "rules": {"quotes": [2, "single", {"avoidEscape": true}], "strict": [2, "never"], "eol-last": [0], "no-mixed-requires": [0], "no-underscore-dangle": [0], "no-bitwise": 2, "camelcase": ["error", {"allow": ["^UNSAFE_"]}], "eqeqeq": 2, "wrap-iife": [2, "inside"], "no-use-before-define": [2, "nofunc"], "no-caller": 2, "no-undef": 2, "new-cap": ["error", {"capIsNewExceptionPattern": "UNSAFE_"}], "react/jsx-uses-react": 2, "react/jsx-uses-vars": 2, "react/react-in-jsx-scope": 2, "react/jsx-no-target-blank": 2}, "plugins": ["react"], "overrides": [{"files": ["**/__tests__/**/*.test.[jt]s?(x)"], "extends": ["plugin:testing-library/react", "plugin:jest-dom/recommended"], "rules": {"testing-library/prefer-presence-queries": "warn"}}]}