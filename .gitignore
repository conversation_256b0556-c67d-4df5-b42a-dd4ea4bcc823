# Folder view configuration files
.DS_Store
Desktop.ini

# Files used by IntelliJ
*.iml
.idea

# Thumbnail cache files
._*
Thumbs.db

# Files that might appear on external disks
.Spotlight-V100
.Trashes

# Application specific files
*.log
node_modules
tmp
deploy/
dist/
config/*
!config/local.sh
!config/local.example.js
!config/startLocal.sh
test/bin
web/
artifact_node.sh
coverage/
packageMounts/**/*
!packageMounts/**/stub

# Use yarn.lock
package-lock.json

# Ignore old translation files
locales/*/*_old.json

# npm-link-up
.nlu.json

# yarn (ref https://yarnpkg.com/getting-started/qa#which-files-should-be-gitignored)
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

.codegpt
*.code-workspace
