/**
 * Copyright (c) 2014, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of ME<PERSON><PERSON>NTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 */

.people-list-item {
  margin-bottom: @spacing-small;
}

.patient-list-item {
  width: 320px;
  float: left;
  font-size: @font-size-large;
  font-weight: lighter;
  margin-bottom: @spacing-small;
}

.patient-list-controls {
  min-height: 20px;
  text-align: center;
  padding-top: 20px;

  button {
    color: @gray-dark;

    &:hover,
    &:focus,
    &:active {
      text-decoration: underline;
      color: @blue-green-light;
    }
  }
}

.patient-list-controls-button {
  display: inline-block;
  margin-left: 10px;
  line-height: 20px;
  height: 22px;
  padding: 0 8px;
}

.patient-list-controls-button--secondary {
  font-size: 12px;
  font-weight: normal;
  color: @gray-text;
  background: none;
  border-color: transparent;
}

.people-list-single {
  width: 350px;
  margin: 0 auto;
  padding: 0px;
}
