/**
 * Copyright (c) 2014, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERC<PERSON>NTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 */

@notification-max-width:         800px;
@notification-close-link-height: 30px;
@notification-close-link-width:  41px;

.notification {
  position: fixed;
  z-index: @zindex-notification;
  top: @spacing-small;
  left: 0;
  right: 0;
  text-align: center;

  // Since this "transparent" div can cover links, buttons, etc.
  // give it 0 height to allow clicking "through" it
  // (`.notification-inner` will still show)
  height: 0;
}

.notification-inner {
  display: inline-block;
  text-align: center;
  margin-left: @spacing-small;
  margin-right: @spacing-small;
  max-width: @notification-max-width;
  padding: 0 @spacing-small;
  border: 2px solid @gray-light;
  background-color: #fff;
  border-radius: 4px;

  a,
  a:hover,
  a:focus {
    color: @text-color;
    text-decoration: underline;
  }
}

.notification-success .notification-inner {
  color: @state-success-text;
  background-color: @state-success-bg;
  border-color: @state-success-border;

  a,
  a:hover,
  a:focus {
    color: @state-success-text;
  }
}

.notification-alert .notification-inner {
  color: @state-alert-text;
  background-color: @state-alert-bg;
  border-color: @state-alert-border;

  a,
  a:hover,
  a:focus {
    color: @state-alert-text;
  }
}

.notification-error .notification-inner {
  color: @state-error-text;
  background-color: @state-error-bg;
  border-color: @state-error-border;

  a,
  a:hover,
  a:focus {
    color: @state-error-text;
  }
}

.notification-closable .notification-inner {
  // For absolute-positioned close link
  position: relative;
  padding-right: (@notification-close-link-width + 4px);
}

.notification-close {
  display: block;
  position: absolute;
  top: 5px;
  right: 0;

  font-size: 12px;
  line-height: @notification-close-link-height;
  padding: 0 5px;
}

.notification-body {
  margin: @spacing-small 0;

  // Default paragraph styles for notification body
  p {
    margin: 0;
    margin-bottom: @spacing-small;
  }
}

.notification-body-small {
  font-size: 12px;
  line-height: 16px;
  color: @gray-dark;
}
