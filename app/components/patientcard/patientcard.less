/**
 * Copyright (c) 2014, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 */

.patientcard-actions--highlight {
  text-decoration: underline;
}

.patient-list-item {
  .no-touch &:hover,
  &:focus,
  &:active {
    .patientcard-leave {
      display: block;
    }
    .patientcard-fullname {
    }
  }

}

.patientcard.isEditing .patientcard-leave {
  display: inline;
}

.icon-face-standin {
  color: @blue-green;
}

.patientcard {
  display: block;
  text-align: left;
  padding: 30px @spacing-small;
  font-weight: lighter;
  color: @gray-darker;
  position: relative;
  width: 330px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.patientcard-owner {
  background: @owned-account;
}

.patientcard {
  display: flex;
  align-items: center;

  > a {
    margin-left: 10px;
    float: left;
    width: 260px;
    color: @gray-darker;
    text-decoration: none;
    text-align: left;
    display: block;
    height: 33px;
  }

  a.patientcard-fullname-link {
    display: inline-block;
    margin-bottom: 4px;
  }

  > a.patientcard-icon {
    width: auto;
    height: auto;
  }

  .icon-face-standin {
    font-size: 45px;
    width: 65px;
    float: left;
    margin-top: 5px;
  }
}

.patientcard-fullname {
  padding-top: 5px;
  padding-left: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 205px;
  color: @gray-dark;
  font-style: normal;
}

.patientcard-dateOfBirth {
  padding-left: 5px;
  max-width: 205px;
  color: @gray-dark;
  font-size: @font-size-small;
}

.patientcard-leave {
  display: none;
  margin-top: 3px;
  margin-left: 5px;


  .icon-delete {
    color: red;
    font-size: 14px;
    opacity: 0.5;

    .no-touch &:hover,
    &:focus,
    &:active {
        opacity: 1;
    }
  }
}

.patientcard-info, .patientcard-leave {
  float: left;
}

.patientcard-actions {
  font-size: @font-size-small;

  > a {
    padding: 10px 5px;
    color: @gray-dark;

    .no-touch &:hover,
    &:focus,
    &:active {
        color: @blue-green-light;
    }
  }
}

.patientcard,
a.patientcard {
  color: @gray-darker;
  text-decoration: none;
}

.patientcard-owner.patientcard,
.patientcard-owner.a.patientcard {
  .no-touch &:hover,
  &:focus,
  &:active {
    background-color: @owned-account-hover;

    .icon-face-standin {
      color: @gray-darker;
    }

    .patientcard-leave {
      display: block;
    }
  }
}

.navbarpatientcard-profile {
  text-decoration: none;

  .no-touch &:hover,
  &:focus,
  &:active {
    text-decoration: none;

    .patientcard-fullname {
      color: @blue-green-light;
      text-decoration: underline;
    }

    i {
      color: @blue-green-light;
    }
  }
}

.patientcard-actions--highlight {
  color: @blue-green !important;

  .patientcard-fullname {
    color: @blue-green;
    text-decoration: underline;
  }

  .icon-settings {
    color: @blue-green;
  }

  .no-touch &:hover,
  &:focus,
  &:active {
    .patientcard-fullname {
      color: @blue-green;
    }

    .icon-settings {
      color: @blue-green;
    }
  }
}
