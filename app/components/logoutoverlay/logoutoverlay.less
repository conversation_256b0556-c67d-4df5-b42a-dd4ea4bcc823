/**
 * Copyright (c) 2014, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERC<PERSON>NTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 */

@logout-overlay-font-size:    20px;
@logout-overlay-zindex:       @zindex-overlay-background;
@logout-overlay-text-zindex:  @zindex-overlay;

.logout-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #fff;
  z-index: @logout-overlay-zindex;
  overflow-y: scroll;

  opacity: 1;
  .transition-property(opacity);
  .transition-duration(.2s);
  .transition-timing-function(linear);
}

.logout-overlay-fade-out {
  opacity: 0;
}

.logout-overlay-text {
  font-size: @logout-overlay-font-size;
  line-height: 1;

  position: fixed;
  top: 50%;
  margin-top: -(@logout-overlay-font-size/2);
  left: 0;
  width: 100%;
  text-align: center;
  z-index: @logout-overlay-text-zindex;
}
