.ModalOverlay {
  display: none;

  .ModalOverlay-target {
    content: "";
    display: none;
    background: rgba(0, 0, 0, 0.6);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 11;
  }

  .PatientInfo-button--warning {
    background-color: @brand-coral;
    border-color: @brand-coral;

    .no-touch &:hover,
    &:focus,
    &:active {
      background-color: @brand-orange;
      border-color: @brand-orange;
    }
  }
}

.ModalOverlay--show {
  display: block;

  .ModalOverlay-target {
    cursor: pointer;
    display: block;
  }
}

.ModalOverlay-dialog {
  background: #fefefe;
  border: @gray solid 1px;
  margin-left: -200px;
  position: fixed;
  left: 50%;
  top: 20%;
  z-index: 12;
  width: 420px;
  padding: 20px 25px;
  border-radius: 10px;
  white-space: normal;
}

.ModalOverlay-content {
  padding-bottom: 10px;
  font-size: @font-size-base;

  > .input-group {
    padding-left: 0px;
    margin-bottom: 0px;

    .input-group-radios {
      margin-bottom: 0px;
      margin-top: 15px;
      font-size: @font-size-base;
    }

    label {
      padding: 5px 20px;
    }
  }
}
.ModalOverlay-controls {
  min-height: 30px;
  text-align: right;
}

.ModalOverlay-dismiss {
  font-size: 30px;
  font-weight: 500;
  color: #6d6d6d;
  cursor: pointer;
  position: absolute;
  top: 16px;
  right: 20px;

  &:hover {
    text-decoration: none;
    color: #627cff;
  }
}
