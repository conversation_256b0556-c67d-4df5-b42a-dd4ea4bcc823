/**
 * Copyright (c) 2014, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERC<PERSON><PERSON>ABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 */

/* global __TEST__ */

if (__TEST__ && (window === undefined || window.config === undefined)) {
  // Need to add this line as some files include config which
  // errors if window.config does not exist during test situations
  window.config = {};
}

var config = window.config;

if (!config) {
  throw new Error('Expected `config` on the global `window` object');
}

module.exports = config;
