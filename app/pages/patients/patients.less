 /**
 * Copyright (c) 2014, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 */

 @patient-section-title-width: 260px;
 @mobile-breakpoint: 768px;

.patients,
.patients-welcome {
  margin-right: auto;
  margin-left: auto;
  margin-bottom: @spacing-large;
  background: white;
  border: 1px solid @gray-light;
  position: relative;
}

.patients-welcome {
  background: none;
  border: 0;
}

.patients-section {
  position: relative;
  background-color: #fff;
  border-top: 1px solid @gray-light;
}

.patients-section-title-wrapper {
  display: grid;
  grid-template-columns: 1fr @patient-section-title-width 1fr;
  margin-bottom: @spacing-base;

  @media screen and (max-width: @mobile-breakpoint) {
    grid-template-columns: 1fr;
  }
}

.patients-section-title {
  display: inline-block;
  min-width: @patient-section-title-width;
  background-color: @gray-light;
  font-weight: 200;
  padding: @spacing-small;

  @media screen and (max-width: @mobile-breakpoint) {
    text-align: center;
    margin: 0 @spacing-large;
  }
}

.patients-section-content {
  width: 100%;
  max-width: 880px;
  margin: 0 auto;
  padding-bottom: @spacing-base;

  .people-list {
    display: grid;
    grid-gap: 20px;
    grid-template-columns: repeat(1, 1fr);
    justify-items: center;

    @media (min-width: @screen-md-lg-min) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (min-width: @screen-lg-min) {
      grid-template-columns: repeat(3, 1fr);
    }
  }
}

.patients-vca-section-content {
  padding-top: @spacing-large;
  width: 100%;
  max-width: 880px;
  margin: 0 auto;
  padding-bottom: @spacing-base;

  .ModalOverlay-target {
    z-index: 101;
  }

  .ModalOverlay-dialog {
    z-index: 102;
  }
}

.patients-new-account {
  margin-left: auto;
  margin-top: 16px;

  @media screen and (max-width: @mobile-breakpoint) {
    margin-right: auto;
  }

  > span {
    padding-right: 4px;
  }

  .icon-add {
    color: @blue-green;
  }

  .no-touch &:hover,
  &:focus,
  &:active {
    text-decoration: none;
    color: @blue-green-light;

    .icon-add {
      color: @blue-green-light;
    }
  }
}

.patients-section-dismiss {
  position: absolute;
  top: 5px;
  right: 5px;
}

// To be replaced by icon font
.patients-section-dismiss .patients-icon-close:before {
  font-style: normal;
  font-weight: normal;
  speak: none;

  display: inline-block;
  text-decoration: inherit;
  text-align: center;

  content: '✕';
  font-size: 13px;
  line-height: 20px;
  width: 20px;
  height: 20px;
  border-radius: 10px;
}

.patients-section-dismiss a .patients-icon-close:before {
  color: #fff;
  background-color: @gray-dark;
}

.patients-section-dismiss a:hover .patients-icon-close:before,
.patients-section-dismiss a:focus .patients-icon-close:before {
  color: #fff;
  background-color: @gray-darker;
  text-decoration: none;
}

// Messages
// ====================================

.patients-message {
  color: @gray-text;
  font-weight: 300;
  text-align: center;

  margin-top: 60px;
  margin-bottom: 60px;
}

.patients-message-small {
  font-size: 14px;
}

.patients-icon-link {
  color: @blue-green;
}

.patients-message-loading {
  font-weight: normal;
  margin-top: 100px;
  margin-bottom: 100px;
}

// Welcome messages
// ====================================

.patients-welcome-title {
  font-size: 22px;
  line-height: 1;
  text-align: center;
  margin: 20px 0;
  color: @blue-green;
}

.patients-welcomesetup-actions {
  margin-top: 30px;

  button {
    min-width: 180px;
    margin: 10px 10px;

    .btn .btn-primary {
      float: left;
    }
  }
}

.patients-welcomesetup-actions-help {
  font-size: @font-size-small;
}
