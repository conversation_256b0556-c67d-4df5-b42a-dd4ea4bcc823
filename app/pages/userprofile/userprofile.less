/**
 * Copyright (c) 2014, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERC<PERSON>NTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 */

@profile-subnav-vertical-padding:  @spacing-small;
@profile-form-width:               260px;
// Match `.container-small` breakpoint
@profile-form-breakpoint:          (2*130px + @profile-form-width);
@mobile-breakpoint: 768px;

.profile {
  @media screen and (max-width: @mobile-breakpoint) {
    border-top: 1px solid #D9D9D9;
  }
}

.profile-subnav {
  @media screen and (max-width: @mobile-breakpoint) {
    display: none;
  }
}

.profile-subnav-box {
  color: #fff;
  background-color: @gray-dark;
}

.profile-subnav-title {
  padding-top: @profile-subnav-vertical-padding;
  padding-bottom: @profile-subnav-vertical-padding;

  text-align: center;
}

.profile-subnav a {
  display: inline-block;
  padding-top: @profile-subnav-vertical-padding;
  padding-bottom: @profile-subnav-vertical-padding;

  color: #fff;
  text-decoration: none;

  &:hover,
  &:focus {
    color: @gray-darkest;
    text-decoration: none;
  }

  &:active {
    color: @gray;
  }
}

.profile-content {
  margin-bottom: @spacing-large;
}

.profile-content-box {
  padding-top: @spacing-base;
  padding-bottom: @spacing-large;

  background-color: #fff;
}

.profile-form {
  margin-bottom: @spacing-large;

  @media(min-width: @profile-form-breakpoint) {
    margin-left: auto;
    margin-right: auto;
    width: @profile-form-width;
  }

   & :disabled {
    background-color: @gray;
  }
}

// Care Team
// ====================================

.profile-careteam {
  max-width: 480px;
  margin: auto;
  margin-bottom: @spacing-base;
}

.profile-careteam-title {
  margin-bottom: @spacing-small;
  text-align: center;
}

.profile-careteam-message {
  color: @gray-dark;
  font-weight: 300;
  text-align: center;

  margin-top: @spacing-base;
  margin-bottom: @spacing-base;
}

.profile-careteam-icon-link {
  color: @blue-green;
}
