/**
 * Copyright (c) 2014, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERC<PERSON>NTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 */

.PasswordReset {
  .flex-layout-for-logged-out-routes(575px, 0px);
}

.PasswordReset-form-container {
  min-height: 358px;
  margin-bottom: @spacing-large;
}

.PasswordReset-title {
  margin-bottom: 20px;
  font-weight: bold;
  font-size: 20px;
  line-height: 1;
}

.PasswordReset-instructions {
  margin-bottom: 20px;
}

.PasswordReset-form {
  margin-bottom: 10px;
}

.PasswordReset-link {
  font-size: 14px;
  text-align: right;

  a {
    &:extend(.link-secondary all);
  }
}

.PasswordReset-button {
  text-align: center;
}
