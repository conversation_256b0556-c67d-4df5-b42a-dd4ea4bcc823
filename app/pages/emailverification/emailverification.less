/**
 * Copyright (c) 2014, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 */

@breakWidth: 1000px;
@sideMargin: 50px;

.EmailVerification {
  .flex-layout-for-logged-out-routes(575px, 0px);
}

.EmailVerification-content {
  margin-bottom: 6px;
}

.EmailVerification-intro {
  margin: 10px auto 0;
  width: @breakWidth;
  line-height: 1.5;

  @media (max-width: @breakWidth) {
    width: 100%;
  }
}

.EmailVerification-title {
  max-width: 300px;
  margin: @spacing-large auto;
  font-size: 20px;
  font-weight: 600;
  text-align: center;
  color: @purple-dark;


  @media (max-width: @breakWidth) {
    max-width: 100%;
    margin: @spacing-base @sideMargin;
  }
}

.EmailVerification-instructions {
  max-width: 600px;
  margin: 0 auto @spacing-base;
  margin-bottom: 20px;
  font-size: 18px;
  text-align: center;
  color: @purple-dark;

  @media (max-width: @breakWidth) {
    max-width: 100%;
    margin-left: @sideMargin;
    margin-right: @sideMargin;
    word-break: break-word;
  }
}

.EmailVerification-resend-note {
  font-size: 14px;
  text-align: center;
}

.EmailVerification-form {
  margin-bottom: 10px;
}

.EmailVerification-link {
  font-size: 14px;
  text-align: right;

  a {
    &:extend(.link-secondary all);
  }
}

.EmailVerification-button {
  text-align: center;
}
