/**
 * Copyright (c) 2014, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 */


// Subnav
// ====================================

@PatientNew-subnavPadding: @spacing-small;

.PatientNew-subnavInner {
  color: #fff;
  background-color: @gray-dark;
}

.PatientNew-subnavTitle {
  padding-top: @PatientNew-subnavPadding;
  padding-bottom: @PatientNew-subnavPadding;

  text-align: center;
}

.PatientNew-subnav a {
  display: inline-block;
  padding-top: @PatientNew-subnavPadding;
  padding-bottom: @PatientNew-subnavPadding;

  color: #fff;
  text-decoration: none;

  &:hover,
  &:focus {
    color: @gray-darkest;
    text-decoration: none;
  }

  &:active {
    color: @gray;
  }
}

// Content
// ====================================

@PatientNew-contentWidth: 440px;

.PatientNew-contentOuter {
  margin-bottom: @spacing-large;
}

.PatientNew-contentInner {
  background-color: #fff;

  padding-top: @spacing-large;
  padding-bottom: @spacing-large;
}

.PatientNew-content {
  margin-left: auto;
  margin-right: auto;
  width: @PatientNew-contentWidth;
  .input-group:nth-of-type(7) {
    padding-top: 15px;
  }
  .input-group-explanation {
    margin-left: 0;
    margin-right: 0;
    margin-top: 0px;
    color: @gray-dark;
    font-size: 14px;
    text-align: left;
  }
}

// Form
// ====================================

.PatientNew-form .input-group {
  margin: 0;
}

.PatientNew-inputGroup {
  margin-bottom: 30px;
}

.PatientNew-inputGroup--fullName {
  margin-bottom: 5px;
}

.PatientNew-inputGroup--birthday {
  margin-bottom: 10px;
}

.PatientNew-datePickerMessage {
  &:extend(.form-help-block);
}

.PatientNew-datePicker--error {
  .DatePicker-control {
    border-color: @red-error;
  }

  .PatientNew-datePickerMessage {
    color: @red-error;
  }
}

.PatientNew-formActions {
  text-align: right;
  margin-top: 40px;
}

a.PatientNew-cancel {
  margin-right: 10px;
}

// Notification
// ====================================

.PatientNew-notification {
  margin-top: @spacing-small;
}

.PatientNew-notification--error {
  color: @red-error;
}
