
/**
 * Copyright (c) 2017, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 */

import PropTypes from 'prop-types';
import React, { Component } from 'react';

const CustomizedTrendsChart = (props) => {
  return (
    <svg width="571" height="183" viewBox="835 909 571 183">
      <text fill="none" fontFamily="Basis, Helvetica Neue, Helvetica, Arial, sans-serif" fontSize="14">
        <tspan x="835" y="990" fill="#58595B" textAnchor="start">{props.max}</tspan>
      </text>
      <text fill="none" fontFamily="Basis, Helvetica Neue, Helvetica, Arial, sans-serif" fontSize="14">
        <tspan x="835" y="1045" fill="#58595B" textAnchor="start">{props.min}</tspan>
      </text>
      <g fill="none" fillRule="evenodd">
        <path fill="#E3EBED" d="M878 938.36h107.308v153.25H878"/>
        <path fill="#E9EFF1" d="M985.308 938.36h107.307v153.25H985.308"/>
        <path fill="#F2F4F6" d="M1092.615 938.36h107.308v153.25h-107.308"/>
        <path fill="#E9EFF1" d="M1199.923 938.36h107.308v153.25h-107.3"/>
        <path d="M1305.554 988.772h1.677v-1.216h-1.67v1.216zm-3.912 0h1.676v-1.216h-1.676v1.216zm-3.912 0h1.676v-1.216h-1.676v1.216zm-3.913 0h1.677v-1.216h-1.677v1.216zm-3.912 0h1.677v-1.216h-1.677v1.216zm-3.912 0h1.677v-1.216h-1.677v1.216zm-3.912 0h1.68v-1.216h-1.67v1.216zm-3.91 0h1.68v-1.216h-1.67v1.216zm-3.91 0h1.68v-1.216h-1.67v1.216zm-3.91 0h1.68v-1.216h-1.67v1.216zm-3.91 0h1.68v-1.216h-1.67v1.216zm-3.91 0h1.68v-1.216h-1.67v1.216zm-3.91 0h1.68v-1.216h-1.67v1.216zm-3.91 0h1.68v-1.216h-1.67v1.216zm-3.91 0h1.68v-1.216h-1.67v1.216zm-3.91 0h1.68v-1.216h-1.67v1.216zm-3.91 0h1.68v-1.216h-1.68v1.216zm-3.91 0h1.68v-1.216h-1.67v1.216zm-3.91 0h1.68v-1.216h-1.67v1.216zm-3.91 0h1.68v-1.216h-1.67v1.216zm-3.91 0h1.67v-1.216h-1.67v1.216zm-3.91 0h1.68v-1.216h-1.67v1.216zm-3.91 0h1.68v-1.216h-1.67v1.216zm-3.91 0h1.68v-1.216h-1.68v1.216zm-3.91 0h1.68v-1.216h-1.67v1.216zm-3.91 0h1.68v-1.216h-1.67v1.216zm-3.91 0h1.68v-1.216h-1.67v1.216zm-3.91 0h1.68v-1.216h-1.68v1.216zm-3.91 0h1.68v-1.216h-1.68v1.216zm-3.91 0h1.68v-1.216h-1.67v1.216zm-3.91 0h1.68v-1.216h-1.68v1.216zm-3.91 0h1.67v-1.216h-1.67v1.216zm-3.91 0h1.68v-1.216h-1.68v1.216zm-3.91 0h1.68v-1.216h-1.68v1.216zm-3.91 0h1.68v-1.216h-1.67v1.216zm-3.91 0h1.68v-1.216h-1.68v1.216zm-3.91 0h1.68v-1.216h-1.675v1.216zm-3.91 0h1.68v-1.216h-1.673v1.216zm-3.91 0h1.68v-1.216h-1.68v1.216zm-3.91 0h1.68v-1.216h-1.7v1.216zm-3.91 0h1.677v-1.216h-1.675v1.216zm-3.912 0h1.68v-1.216h-1.68v1.216zm-3.91 0h1.676v-1.216h-1.67v1.216zm-3.913 0h1.676v-1.216h-1.674v1.216zm-3.913 0h1.674v-1.216h-1.68v1.216zm-3.915 0h1.678v-1.216h-1.68v1.216zm-3.91 0h1.677v-1.216h-1.67v1.216zm-3.91 0h1.68v-1.216h-1.68v1.216zm-3.91 0h1.675v-1.216h-1.68v1.216zm-3.914 0h1.674v-1.216h-1.68v1.216zm-3.915 0h1.68v-1.216H1110v1.216zm-3.91 0h1.675v-1.216h-1.676v1.216zm-3.91 0h1.673v-1.216h-1.673v1.216zm-3.916 0h1.68v-1.216h-1.676v1.216zm-3.91 0h1.67v-1.216h-1.68v1.216zm-3.915 0h1.672v-1.216h-1.68v1.216zm-3.917 0h1.676v-1.216h-1.676v1.216zm-3.91 0h1.674v-1.216h-1.68v1.216zm-3.915 0h1.675v-1.216h-1.68v1.216zm-3.914 0h1.68v-1.216h-1.68v1.216zm-3.91 0h1.67v-1.216h-1.673v1.216zm-3.92 0h1.678v-1.216h-1.673v1.216zm-3.91 0h1.676v-1.216h-1.68v1.216zm-3.91 0h1.68v-1.216h-1.68v1.216zm-3.91 0h1.675v-1.216h-1.676v1.216zm-3.91 0h1.67v-1.216h-1.674v1.216zm-3.917 0h1.68v-1.216h-1.68v1.216zm-3.91 0h1.677v-1.216h-1.676v1.216zm-3.91 0h1.676v-1.216h-1.677v1.216zm-3.91 0h1.67v-1.216h-1.68v1.216zm-3.914 0h1.675v-1.216h-1.68v1.216zm-3.914 0h1.68v-1.216h-1.68v1.216zm-3.91 0h1.68v-1.216h-1.68v1.216zm-3.91 0h1.676v-1.216h-1.7v1.216zm-3.91 0h1.68v-1.216h-1.68v1.216zm-3.91 0h1.68v-1.216h-1.68v1.216zm-3.91 0h1.674v-1.216h-1.68v1.216zm-3.915 0h1.68v-1.216h-1.675v1.216zm-3.91 0h1.68v-1.216h-1.675v1.216zm-3.91 0h1.68v-1.216h-1.69v1.216zm-3.91 0h1.676v-1.216h-1.68v1.216zm-3.913 0h1.68v-1.216h-1.68v1.216zm-3.91 0h1.678v-1.216h-1.68v1.216zm-3.91 0h1.677v-1.216h-1.68v1.216zm-3.91 0h1.674v-1.216H977v1.216zm-3.915 0h1.676v-1.216h-1.68v1.216zm-3.91 0h1.675v-1.216h-1.71v1.216zm-3.91 0h1.673v-1.216h-1.69v1.216zm-3.916 0H963v-1.216h-1.68v1.216zm-3.916 0h1.68v-1.216h-1.69v1.216zm-3.91 0h1.68v-1.216h-1.676v1.216zm-3.91 0h1.677v-1.216h-1.68v1.216zm-3.91 0h1.68v-1.216h-1.69v1.216zm-3.91 0h1.674v-1.216h-1.68v1.216zm-3.91 0h1.673v-1.216h-1.69v1.216zm-3.91 0h1.67v-1.216h-1.675v1.216zm-3.92 0h1.68v-1.216h-1.68v1.216zm-3.91 0h1.674v-1.216h-1.68v1.216zm-3.91 0h1.673v-1.216h-1.676v1.216zm-3.91 0H920v-1.216h-1.68v1.216zm-3.92 0h1.68v-1.216h-1.68v1.216zm-3.91 0h1.678v-1.216h-1.68v1.216zm-3.91 0h1.677v-1.216H906.6v1.216zm-3.91 0h1.68v-1.216h-1.68v1.216zm-3.91 0h1.68v-1.216h-1.69v1.216zm-3.91 0h1.676v-1.216h-1.68v1.216zm-3.91 0h1.673v-1.216h-1.68v1.216zm-3.916 0h1.678v-1.216H887v1.216zm-3.91 0h1.68v-1.216h-1.68v1.216zm-3.91 0h1.68v-1.216h-1.68v1.216z" stroke="#FFF" strokeWidth="2" fill="#FFF"/>
        <text fontFamily="Basis, Helvetica Neue, Helvetica, Arial, sans-serif" fontSize="14" fill="#727375" transform="translate(878 909)">
          <tspan x="5.707" y="17">6 am</tspan> <tspan x="116.094" y="17">9 am</tspan> <tspan x="225.779" y="17">12 pm</tspan> <tspan x="336.817" y="17">3 pm</tspan>
        </text>
        <path fill="#BAC8D0" d="M878 923.826h1.118v13.417H878m107.308-13.417h1.117v13.417h-1.117m107.307-13.417h1.118v13.417h-1.118m106.19-13.417h1.118v13.417h-1.118m106.19-13.417h1.118v13.417h-1.118"/>
        <path d="M1305.554 1042.44h1.677v-1.218h-1.67v1.217zm-3.912 0h1.676v-1.218h-1.676v1.217zm-3.912 0h1.676v-1.218h-1.676v1.217zm-3.913 0h1.677v-1.218h-1.677v1.217zm-3.912 0h1.677v-1.218h-1.677v1.217zm-3.912 0h1.677v-1.218h-1.677v1.217zm-3.912 0h1.68v-1.218h-1.67v1.217zm-3.91 0h1.68v-1.218h-1.67v1.217zm-3.91 0h1.68v-1.218h-1.67v1.217zm-3.91 0h1.68v-1.218h-1.67v1.217zm-3.91 0h1.68v-1.218h-1.67v1.217zm-3.91 0h1.68v-1.218h-1.67v1.217zm-3.91 0h1.68v-1.218h-1.67v1.217zm-3.91 0h1.68v-1.218h-1.67v1.217zm-3.91 0h1.68v-1.218h-1.67v1.217zm-3.91 0h1.68v-1.218h-1.67v1.217zm-3.91 0h1.68v-1.218h-1.68v1.217zm-3.91 0h1.68v-1.218h-1.67v1.217zm-3.91 0h1.68v-1.218h-1.67v1.217zm-3.91 0h1.68v-1.218h-1.67v1.217zm-3.91 0h1.67v-1.218h-1.67v1.217zm-3.91 0h1.68v-1.218h-1.67v1.217zm-3.91 0h1.68v-1.218h-1.67v1.217zm-3.91 0h1.68v-1.218h-1.68v1.217zm-3.91 0h1.68v-1.218h-1.67v1.217zm-3.91 0h1.68v-1.218h-1.67v1.217zm-3.91 0h1.68v-1.218h-1.67v1.217zm-3.91 0h1.68v-1.218h-1.68v1.217zm-3.91 0h1.68v-1.218h-1.68v1.217zm-3.91 0h1.68v-1.218h-1.67v1.217zm-3.91 0h1.68v-1.218h-1.68v1.217zm-3.91 0h1.67v-1.218h-1.67v1.217zm-3.91 0h1.68v-1.218h-1.68v1.217zm-3.91 0h1.68v-1.218h-1.68v1.217zm-3.91 0h1.68v-1.218h-1.67v1.217zm-3.91 0h1.68v-1.218h-1.68v1.217zm-3.91 0h1.68v-1.218h-1.675v1.217zm-3.91 0h1.68v-1.218h-1.673v1.217zm-3.91 0h1.68v-1.218h-1.68v1.217zm-3.91 0h1.68v-1.218h-1.7v1.217zm-3.91 0h1.677v-1.218h-1.675v1.217zm-3.912 0h1.68v-1.218h-1.68v1.217zm-3.91 0h1.676v-1.218h-1.67v1.217zm-3.913 0h1.676v-1.218h-1.674v1.217zm-3.913 0h1.674v-1.218h-1.68v1.217zm-3.915 0h1.678v-1.218h-1.68v1.217zm-3.91 0h1.677v-1.218h-1.67v1.217zm-3.91 0h1.68v-1.218h-1.68v1.217zm-3.91 0h1.675v-1.218h-1.68v1.217zm-3.914 0h1.674v-1.218h-1.68v1.217zm-3.915 0h1.68v-1.218H1110v1.217zm-3.91 0h1.675v-1.218h-1.676v1.217zm-3.91 0h1.673v-1.218h-1.673v1.217zm-3.916 0h1.68v-1.218h-1.676v1.217zm-3.91 0h1.67v-1.218h-1.68v1.217zm-3.915 0h1.672v-1.218h-1.68v1.217zm-3.917 0h1.676v-1.218h-1.676v1.217zm-3.91 0h1.674v-1.218h-1.68v1.217zm-3.915 0h1.675v-1.218h-1.68v1.217zm-3.914 0h1.68v-1.218h-1.68v1.217zm-3.91 0h1.67v-1.218h-1.673v1.217zm-3.92 0h1.678v-1.218h-1.673v1.217zm-3.91 0h1.676v-1.218h-1.68v1.217zm-3.91 0h1.68v-1.218h-1.68v1.217zm-3.91 0h1.675v-1.218h-1.676v1.217zm-3.91 0h1.67v-1.218h-1.674v1.217zm-3.917 0h1.68v-1.218h-1.68v1.217zm-3.91 0h1.677v-1.218h-1.676v1.217zm-3.91 0h1.676v-1.218h-1.677v1.217zm-3.91 0h1.67v-1.218h-1.68v1.217zm-3.914 0h1.675v-1.218h-1.68v1.217zm-3.914 0h1.68v-1.218h-1.68v1.217zm-3.91 0h1.68v-1.218h-1.68v1.217zm-3.91 0h1.676v-1.218h-1.7v1.217zm-3.91 0h1.68v-1.218h-1.68v1.217zm-3.91 0h1.68v-1.218h-1.68v1.217zm-3.91 0h1.674v-1.218h-1.68v1.217zm-3.915 0h1.68v-1.218h-1.675v1.217zm-3.91 0h1.68v-1.218h-1.675v1.217zm-3.91 0h1.68v-1.218h-1.69v1.217zm-3.91 0h1.676v-1.218h-1.68v1.217zm-3.913 0h1.68v-1.218h-1.68v1.217zm-3.91 0h1.678v-1.218h-1.68v1.217zm-3.91 0h1.677v-1.218h-1.68v1.217zm-3.91 0h1.674v-1.218H977v1.217zm-3.915 0h1.676v-1.218h-1.68v1.217zm-3.91 0h1.675v-1.218h-1.71v1.217zm-3.91 0h1.673v-1.218h-1.69v1.217zm-3.916 0H963v-1.218h-1.68v1.217zm-3.916 0h1.68v-1.218h-1.69v1.217zm-3.91 0h1.68v-1.218h-1.676v1.217zm-3.91 0h1.677v-1.218h-1.68v1.217zm-3.91 0h1.68v-1.218h-1.69v1.217zm-3.91 0h1.674v-1.218h-1.68v1.217zm-3.91 0h1.673v-1.218h-1.69v1.217zm-3.91 0h1.67v-1.218h-1.675v1.217zm-3.92 0h1.68v-1.218h-1.68v1.217zm-3.91 0h1.674v-1.218h-1.68v1.217zm-3.91 0h1.673v-1.218h-1.676v1.217zm-3.91 0H920v-1.218h-1.68v1.217zm-3.92 0h1.68v-1.218h-1.68v1.217zm-3.91 0h1.678v-1.218h-1.68v1.217zm-3.91 0h1.677v-1.218H906.6v1.217zm-3.91 0h1.68v-1.218h-1.68v1.217zm-3.91 0h1.68v-1.218h-1.69v1.217zm-3.91 0h1.676v-1.218h-1.68v1.217zm-3.91 0h1.673v-1.218h-1.68v1.217zm-3.916 0h1.678v-1.218H887v1.217zm-3.91 0h1.68v-1.218h-1.68v1.217zm-3.91 0h1.68v-1.218h-1.68v1.217z" stroke="#FFF" strokeWidth="2" fill="#FFF"/>
        <path d="M884.5 1030.624c2.016 1.228 2.653 3.855 1.424 5.87-1.23 2.015-3.857 2.653-5.872 1.425-2.017-1.23-2.656-3.86-1.427-5.87 1.23-2.02 3.857-2.66 5.876-1.43m9 2c2.02 1.23 2.66 3.85 1.43 5.87-1.23 2.01-3.85 2.65-5.87 1.42-2.01-1.23-2.65-3.86-1.42-5.87 1.23-2.02 3.86-2.66 5.88-1.43" fill="#76D3A6"/>
        <path d="M901.32 1028.697c2.014 1.228 2.653 3.855 1.425 5.87-1.23 2.015-3.86 2.654-5.872 1.424-2.017-1.22-2.656-3.85-1.428-5.86 1.23-2.01 3.86-2.65 5.876-1.42m7.28-7.41c2.02 1.23 2.66 3.86 1.43 5.87-1.23 2.02-3.85 2.65-5.87 1.43-2.01-1.22-2.65-3.85-1.42-5.87 1.23-2.01 3.86-2.65 5.88-1.42m7.28-6.27c2.01 1.23 2.65 3.86 1.42 5.87-1.22 2.01-3.86 2.65-5.87 1.43-2.01-1.22-2.65-3.85-1.42-5.87 1.23-2.01 3.86-2.65 5.88-1.42m6.41-6.27c2.02 1.23 2.66 3.86 1.43 5.87-1.22 2.01-3.85 2.65-5.87 1.42s-2.65-3.86-1.42-5.87c1.23-2.01 3.86-2.65 5.87-1.42m6.41-7.13c2.02 1.23 2.66 3.86 1.43 5.87-1.225 2.02-3.853 2.65-5.87 1.43s-2.654-3.85-1.426-5.87c1.23-2.01 3.855-2.65 5.87-1.42" fill="#76D3A6"/>
        <path d="M937.864 995.522c1.287 1.976.727 4.622-1.252 5.91-1.978 1.285-4.624.724-5.91-1.252-1.29-1.978-.726-4.624 1.248-5.91 1.98-1.287 4.628-.726 5.914 1.252m8.96-3.864c1.286 1.977.726 4.62-1.252 5.907-1.98 1.288-4.626.728-5.915-1.25-1.286-1.975-.726-4.62 1.252-5.91 1.97-1.287 4.62-.725 5.91 1.253m10.41-1.138c1.28 1.98.72 4.625-1.26 5.91-1.98 1.286-4.63.725-5.91-1.253-1.29-1.976-.73-4.622 1.25-5.907 1.97-1.286 4.62-.727 5.91 1.25m9.68 2.866c1.29 1.976.73 4.622-1.25 5.91-1.98 1.285-4.63.724-5.92-1.252-1.29-1.977-.73-4.62 1.25-5.91 1.98-1.287 4.62-.725 5.91 1.252" fill="#6DD5A9"/>
        <path d="M1191.49 948.803c4.508 6.914 2.55 16.175-4.37 20.683-6.93 4.5-16.182 2.534-20.7-4.38-4.508-6.922-2.534-16.176 4.384-20.684 6.925-4.507 16.183-2.54 20.685 4.38" fill="#BB9AE7"/>
        <path d="M995.49 1006.803c4.508 6.914 2.55 16.175-4.37 20.683-6.93 4.5-16.182 2.534-20.7-4.38-4.508-6.922-2.534-16.176 4.384-20.684 6.925-4.507 16.183-2.54 20.685 4.38" fill="#76D3A6"/>
        <path d="M1091.49 1059.803c4.508 6.914 2.55 16.175-4.37 20.683-6.93 4.5-16.182 2.534-20.7-4.38-4.508-6.922-2.534-16.176 4.384-20.684 6.925-4.507 16.183-2.54 20.685 4.38" fill="#FF8A7C"/>
        <path d="M976.324 999.793c1.29 1.976.73 4.622-1.248 5.91-1.98 1.286-4.624.724-5.914-1.252s-.725-4.62 1.252-5.9c1.98-1.29 4.624-.72 5.91 1.25" fill="#6DD5A9"/>
        <path d="M984.7 1010.523c.078 2.358-1.773 4.334-4.132 4.41-2.36.077-4.333-1.77-4.412-4.128-.077-2.355 1.77-4.333 4.13-4.41 2.36-.077 4.334 1.77 4.413 4.128m9.54 4.272c.08 2.358-1.77 4.333-4.14 4.41-2.36.077-4.33-1.77-4.41-4.128-.08-2.358 1.77-4.334 4.13-4.41 2.36-.078 4.34 1.77 4.41 4.128m9.82 8.543c.07 2.357-1.78 4.33-4.13 4.41-2.36.077-4.34-1.77-4.42-4.128-.08-2.358 1.77-4.332 4.12-4.41 2.36-.08 4.33 1.77 4.41 4.128m8.41 4.272c.08 2.357-1.77 4.33-4.13 4.407-2.36.08-4.34-1.77-4.41-4.126-.08-2.35 1.77-4.33 4.13-4.41 2.36-.07 4.33 1.77 4.41 4.13" fill="#76D3A6"/>
        <path d="M1018.885 1031.88c.08 2.356-1.772 4.334-4.13 4.41-2.357.077-4.332-1.77-4.41-4.128-.082-2.358 1.768-4.333 4.13-4.41 2.36-.077 4.333 1.77 4.41 4.128m8.66 1.25c.08 2.358-1.77 4.334-4.13 4.41-2.36.078-4.334-1.77-4.413-4.127-.077-2.356 1.772-4.334 4.13-4.41 2.36-.078 4.334 1.77 4.413 4.128" fill="#76D3A6"/>
        <path d="M1035.528 1031.695c.077 2.358-1.774 4.33-4.13 4.41-2.36.077-4.336-1.77-4.413-4.128-.08-2.358 1.77-4.332 4.128-4.41 2.364-.08 4.336 1.77 4.415 4.128m15.41 2.27c.077 2.36-1.77 4.33-4.13 4.41-2.36.08-4.336-1.77-4.413-4.127-.08-2.358 1.77-4.333 4.13-4.41 2.358-.077 4.334 1.77 4.413 4.128" fill="#76D3A6"/>
        <path d="M1043.938 1029.966c.077 2.358-1.77 4.33-4.13 4.408-2.36.08-4.336-1.77-4.413-4.126-.08-2.358 1.77-4.333 4.13-4.41 2.358-.077 4.334 1.77 4.413 4.128m14.408 9.27c.08 2.357-1.77 4.335-4.13 4.412-2.357.077-4.33-1.77-4.41-4.13-.08-2.356 1.77-4.332 4.13-4.41 2.36-.076 4.333 1.772 4.41 4.127" fill="#76D3A6"/>
        <path d="M1065.043 1042.28c1.31 1.964.778 4.617-1.188 5.926-1.963 1.305-4.615.775-5.925-1.188-1.308-1.962-.776-4.615 1.186-5.922 1.964-1.31 4.618-.777 5.927 1.185m8.97 4.91c1.032 2.12.153 4.68-1.968 5.71-2.124 1.04-4.682.16-5.714-1.96-1.03-2.12-.15-4.68 1.97-5.71 2.13-1.03 4.68-.15 5.72 1.97" fill="#FF8A7C"/>
        <path d="M1082.276 1048.823c1.31 1.965.775 4.62-1.186 5.925-1.966 1.31-4.62.78-5.928-1.186-1.31-1.962-.777-4.613 1.186-5.922 1.968-1.31 4.618-.777 5.928 1.183m8.548-2.133c1.308 1.963.776 4.612-1.19 5.923-1.962 1.307-4.615.775-5.923-1.188-1.31-1.962-.78-4.615 1.19-5.922 1.97-1.31 4.62-.777 5.93 1.186" fill="#FF8A7C"/>
        <path d="M1097.23 1044.555c1.313 1.96.78 4.615-1.185 5.922-1.964 1.307-4.616.775-5.926-1.187-1.31-1.963-.78-4.613 1.18-5.923 1.96-1.31 4.61-.777 5.92 1.188" fill="#FF8A7C"/>
        <path d="M1103.64 1040.283c1.313 1.96.78 4.616-1.185 5.923-1.964 1.307-4.615.775-5.925-1.188-1.31-1.962-.778-4.613 1.188-5.922 1.966-1.31 4.615-.777 5.923 1.187" fill="#FF8A7C"/>
        <path d="M1106.487 1029.835c2.36-.004 4.278 1.908 4.28 4.268.006 2.357-1.906 4.273-4.263 4.275-2.36.004-4.278-1.903-4.282-4.263-.004-2.36 1.906-4.273 4.265-4.28m6.413-10.678c2.358-.007 4.275 1.905 4.28 4.265.003 2.36-1.907 4.27-4.266 4.28-2.36.002-4.278-1.907-4.282-4.265-.004-2.36 1.906-4.276 4.267-4.28m6.4-10.68c2.36-.003 4.28 1.904 4.28 4.264 0 2.37-1.91 4.28-4.27 4.28-2.36.01-4.28-1.9-4.28-4.26-.01-2.36 1.91-4.27 4.26-4.28" fill="#76D3A6"/>
        <path d="M1123.583 1002.07c2.36 0 4.273 1.91 4.282 4.266.002 2.36-1.906 4.276-4.267 4.28-2.36.007-4.278-1.903-4.282-4.263-.004-2.362 1.908-4.275 4.267-4.282" fill="#76D3A6"/>
        <path d="M1127.856 995.664c2.36-.004 4.274 1.905 4.28 4.265.007 2.35-1.906 4.27-4.267 4.27-2.36 0-4.28-1.91-4.28-4.27-.01-2.36 1.9-4.28 4.26-4.28m8.55-2.14c2.36-.01 4.27 1.9 4.28 4.26.01 2.36-1.91 4.27-4.27 4.28-2.36.01-4.28-1.9-4.28-4.26s1.9-4.28 4.26-4.28" fill="#76D3A6"/>
        <path d="M1243.267 970c2.36-.004 4.274 1.905 4.278 4.265.008 2.358-1.904 4.274-4.265 4.278-2.357.006-4.276-1.903-4.28-4.263-.002-2.358 1.906-4.274 4.267-4.28" fill="#BE98E4"/>
        <path d="M1144.95 993.528c2.36-.004 4.274 1.905 4.28 4.265.007 2.358-1.906 4.274-4.267 4.278-2.357.01-4.275-1.9-4.28-4.26-.002-2.35 1.906-4.27 4.267-4.28m8.547-4.27c2.36 0 4.274 1.91 4.278 4.27.01 2.36-1.904 4.28-4.265 4.28-2.357.01-4.275-1.9-4.28-4.26-.002-2.35 1.906-4.27 4.267-4.28" fill="#76D3A6"/>
        <path d="M1157.77 984.986c2.36-.005 4.277 1.905 4.28 4.265.007 2.36-1.905 4.28-4.266 4.28-2.357.01-4.276-1.9-4.28-4.26-.002-2.35 1.906-4.27 4.267-4.28" fill="#76D3A6"/>
        <path d="M1164.18 978.578c2.36-.004 4.277 1.905 4.28 4.265.007 2.358-1.905 4.274-4.266 4.278-2.357.01-4.276-1.9-4.28-4.26-.002-2.35 1.906-4.27 4.267-4.28" fill="#BA9AE7"/>
        <path d="M1170.59 974.307c2.36-.004 4.277 1.905 4.28 4.265.008 2.358-1.905 4.274-4.266 4.278-2.357.006-4.275-1.903-4.28-4.263-.002-2.358 1.906-4.274 4.267-4.28" fill="#BA9AE7"/>
        <path d="M1179.138 976.443c2.36-.005 4.274 1.905 4.28 4.265.007 2.358-1.906 4.273-4.267 4.278-2.35.006-4.27-1.903-4.28-4.263 0-2.358 1.91-4.274 4.27-4.28" fill="#BA9AE7"/>
        <path d="M1187.55 976.714c2.358-.004 4.272 1.905 4.276 4.265.01 2.35-1.903 4.27-4.265 4.27-2.35 0-4.27-1.91-4.27-4.27 0-2.36 1.91-4.28 4.27-4.28" fill="#BA9AE7"/>
        <path d="M1201.835 984.877c-.816 2.213-3.278 3.343-5.492 2.52-2.213-.817-3.34-3.273-2.52-5.488.817-2.22 3.277-3.34 5.488-2.52 2.22.81 3.35 3.27 2.53 5.48m9.45.88c-.82 2.21-3.28 3.34-5.49 2.52-2.21-.82-3.34-3.28-2.52-5.49.82-2.22 3.28-3.34 5.49-2.53 2.22.82 3.35 3.28 2.53 5.49m8.55-4c-.82 2.21-3.28 3.34-5.49 2.52-2.21-.83-3.34-3.28-2.52-5.49.82-2.21 3.28-3.34 5.49-2.52 2.22.82 3.35 3.27 2.53 5.49m9.45-3c-.82 2.21-3.28 3.34-5.49 2.52-2.21-.82-3.34-3.28-2.52-5.49.82-2.22 3.28-3.35 5.5-2.53 2.22.82 3.34 3.28 2.52 5.49m25 0c-.82 2.21-3.28 3.34-5.49 2.52-2.21-.82-3.33-3.28-2.52-5.5.82-2.21 3.28-3.34 5.5-2.52 2.21.82 3.34 3.28 2.52 5.49m-17.21-9.36c2.14.99 3.074 3.53 2.083 5.67-.99 2.14-3.53 3.07-5.67 2.09-2.14-.99-3.08-3.53-2.09-5.67.99-2.15 3.53-3.08 5.67-2.09" fill="#BE98E4"/>
        <path d="M1261.068 973.394c2.14.99 3.075 3.528 2.085 5.67-.99 2.143-3.527 3.076-5.67 2.087-2.144-.99-3.077-3.52-2.088-5.66.99-2.14 3.53-3.08 5.673-2.09" fill="#BB9AE7"/>
        <path d="M1268.544 970.43c2.143.99 3.08 3.53 2.087 5.67-.99 2.144-3.53 3.075-5.67 2.086-2.14-.99-3.07-3.526-2.08-5.668.99-2.142 3.53-3.078 5.67-2.087m8.55-4.27c2.15.99 3.08 3.53 2.09 5.67-.99 2.14-3.53 3.08-5.67 2.09-2.14-.99-3.08-3.52-2.09-5.67.99-2.14 3.53-3.07 5.67-2.09m10.69 0c2.14.99 3.08 3.53 2.09 5.67-.99 2.14-3.53 3.08-5.67 2.09-2.14-.98-3.08-3.52-2.09-5.67.99-2.14 3.53-3.07 5.68-2.08m6.41 6.41c2.14.99 3.08 3.53 2.09 5.67-.99 2.14-3.52 3.08-5.67 2.09-2.14-.98-3.08-3.52-2.09-5.66.99-2.14 3.53-3.07 5.68-2.09m8.55 4.27c2.14.99 3.08 3.53 2.09 5.67-.99 2.14-3.52 3.08-5.67 2.09-2.14-.98-3.08-3.52-2.09-5.67.99-2.14 3.53-3.07 5.68-2.08" fill="#BB9AE7"/>
      </g>
    </svg>
  );
}

CustomizedTrendsChart.propTypes = {
  max: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string, // mmol/L values need to be a string to display as 'trailing zero' floats
  ]).isRequired,

  min: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.string, // mmol/L values need to be a string to display as 'trailing zero' floats
  ]).isRequired,
};

export default CustomizedTrendsChart;
