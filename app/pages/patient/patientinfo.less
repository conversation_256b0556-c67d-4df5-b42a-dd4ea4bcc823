/**
 * Copyright (c) 2014, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 */

.PatientInfo {
  font-size: @font-size-small;
  line-height: 20px;
  color: @gray-text;
}

.PatientInfo-content {
  margin-bottom: 30px;
  padding-left: 175px;
  padding-bottom: @spacing-base;
  border-bottom: 1px solid @gray-rule;
}

.PatientInfo-head {
  .clearfix();
  margin-bottom: 20px;
}

@PatientInfo-pictureSize: 90px;

.PatientInfo-picture {
  float: left;
  margin-right: 30px;
  margin-top: 5px;

  background-image: url('images/profile-180x180.png');
  background-repeat: no-repeat;
  background-size: @PatientInfo-pictureSize;
  width: @PatientInfo-pictureSize;
  height: @PatientInfo-pictureSize;
}

.PatientInfo-blocks {
  // Makes text stay aligned on the right
  // and doesn't wrap under floating picture
  display: table-cell;
  // Force element to stretch to width of outer box
  width: 10000px;

  padding-top: 10px;
}

.PatientInfo-blocks .PatientInfo-blockRow:not(:last-of-type) {
  margin-bottom: 10px;
}

@PatientInfo-blockHeight: 20px;

.PatientInfo-block {
  display: inline-block;
  line-height: @PatientInfo-blockHeight;
  color: #fff;
  background-color: @gray-dark;
  padding-left: 8px;
  padding-right: 10px;

  & a,
  & a:hover,
  & a:focus {
    color: #fff;
    text-decoration: underline;
  }
}

// http://cssarrowplease.com/
.PatientInfo-block--withArrow {
  position: relative;

  &:after {
    right: 100%;
    top: 50%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-color: rgba(152, 152, 151, 0);
    border-right-color: @gray-dark;
    border-width: @PatientInfo-blockHeight/2;
    margin-top: -@PatientInfo-blockHeight/2;
  }
}

a.PatientInfo-block {
  color: #fff;
  text-decoration: none;

  &:hover,
  &:focus {
    background-color: @gray-darkest;

    &.PatientInfo-block--withArrow:after {
      border-right-color: @gray-darkest;
    }
  }
}

.PatientInfo-block--placeholder {
  width: 110px;
}

.PatientInfo-bio {
  word-break: break-all;
  word-break: break-word;
  hyphens: auto;
}

.PatientInfo-bio--placeholder {
  height: 40px;
}

.PatientInfo-controls {
  min-height: 20px;
  text-align: right;
}

// Editing
// ====================================

.PatientInfo-label {
  display: inline-block;
  margin-right: 10px;
}

@PatientInfo-inputHeight: 20px;
@PatientInfo-inputBorderSize: 1px;

.PatientInfo-input {
  display: inline-block;

  font-size: @font-size-small;
  height: @PatientInfo-inputHeight;
  line-height: @PatientInfo-inputHeight - 2*@PatientInfo-inputBorderSize;
  vertical-align: middle;
  padding: 0 8px;
  border: @PatientInfo-inputBorderSize dashed @gray-darker;
  color: @gray-darker;
  background-color: @gray-lighter;

  &:hover {
    border-color: darken(@gray-darker, 15%);
  }

  &:focus {
    // Disable default WebKit focus style
    outline: 0;

    border-color: @blue-green;
  }

  select& {
    border-radius: 0px;
    border: 0;
    outline: 1px dashed #6d6d6d;
    outline-offset: -1px;

    &:focus {
      outline: 1px dashed @blue-green;
    }
  }

  textarea& {
    // Remove inset shadow in iOS
    -webkit-appearance: none;

    height: auto;
    display: block;
    width: 100%;
  }

  input& {
    // Remove inset shadow in iOS
    -webkit-appearance: none;
  }
}

.PatientInfo-input[id="email"]:invalid {
  border: solid 1px #6d6d6d;
  background-color: #dcdff9;
}

.PatientInfo-input-error {
  border-color: @red-error;
}

.PatientInfo-error-message {
  color: @red-error;
}

.PatientInfo-input[id="fullName"] {
  width: 160px;
}

.PatientInfo-input[id="birthday"],
.PatientInfo-input[id="diagnosisDate"] {
  width: 110px;
}

.PatientInfo-button {
  &:extend(.btn all);
  line-height: 20px;
}

.PatientInfo-button--primary {
  &:extend(.btn-primary all);
}

.PatientInfo-button--secondary {
  &:extend(.btn-secondary all);
  font-size: @font-size-tiny;
}

.PatientInfo-notification {
  margin-top: 5px;
}

.PatientInfo-notification--alert {
  color: @orange-alert;
}

.PatientInfo-notification--error {
  color: @red-error;
}

.PatientInfo-notification--success {
  color: @purple-success;
}
