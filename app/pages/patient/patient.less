/**
 * Copyright (c) 2014, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 */

.PatientPage {
  margin-right: auto;
  margin-left: auto;
  margin-bottom: 40px;

  @media(min-width: @screen-lg-min) {
    width: @screen-lg-min;
  }
}

// Subnav
// ====================================

@PatientPage-subnavPadding:  10px;

.PatientPage-subnav {
  height: 40px;
  color: #fff;
  background-color: @blue-gray-dark;
  border-radius: 3px 3px 0 0;
}

.PatientPage-subnavTitle {
  padding-top: @PatientPage-subnavPadding;
  padding-bottom: @PatientPage-subnavPadding;

  text-align: center;
}

.PatientPage-subnav a {
  display: inline-block;
  padding-top: @PatientPage-subnavPadding;
  padding-bottom: @PatientPage-subnavPadding;

  color: #fff;
  text-decoration: none;

  &:hover,
  &:focus {
    color: @gray-darkest;
    text-decoration: none;
  }

  &:active {
    color: @gray;
  }
}

// Content
// ====================================

.PatientPage-content {
  padding-top: 30px;
  padding-bottom: 140px;
  padding-left: 125px;
  padding-right: 125px;

  background-color: @gray-background;
}

.PatientPage-sectionTitle {
  color: @gray-title;
  text-transform: uppercase;
  float: left;
  padding-bottom: 15px;

  .PatientPage-sectionTitle--lowercase {
    text-transform: none;
  }
}

// Footer
// ====================================

.PatientPage-footer {
  background-color: @gray-light;
  height: 40px;
}

// Patient info
// ====================================

.PatientPage-infoSection {
}

.PatientPage-teamSection {
  padding-top: 10px;
}

// Patient delete
// ====================================
.PatientPage-deleteSection {
  > div {
    font-size: @font-size-base;
    text-align: center;
    padding-top: 30px;
    color: @gray-dark;
    cursor: pointer;
  }
}
