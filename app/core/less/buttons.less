/**
 * Copyright (c) 2014, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 */

@btn-border-size:  2px;

// Base button
// ====================================

.btn,
a.btn {
  display: inline-block;
  margin: 0;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  white-space: nowrap;
  background-image: none;

  border: 0px solid @gray-dark;
  border-radius: 4px;

  font-size: @font-size-small;
  line-height: (1.5*@spacing-base) - 2*@btn-border-size;
  padding: 7px (1.5*@spacing-base - 2*@btn-border-size);

  &:hover,
  &:focus {
    outline: 0;
    text-decoration: none;
  }

  &:active,
  &.active {
    outline: 0;
    background-image: none;
  }

  &.disabled,
  &[disabled],
  fieldset[disabled] & {
    cursor: not-allowed;
    pointer-events: none; // Future-proof disabling of clicks
    opacity: .65;
  }
}

// Button styles
// ====================================

// Main action button style
.btn-primary,
a.btn-primary {
  background-color: @purple;
  color: @white-color;

  &:hover,
  &:focus,
  &:active {
    background-color: @purple-dark;
  }
}

.btn-secondary,
a.btn-secondary {
  font-weight: normal;
  color: @gray-dark;
  background: none;
  border-color: transparent;

  &:hover,
  &:focus,
  &:active {
    text-decoration: none;
    color: @purple;
  }
}

.btn-tertiary,
a.btn-tertiary {
  background-color: @white-color;
  border-color: @blue-green;
  color: @blue-green;

  &:hover,
  &:focus,
  &:active {
    border-color: @blue-green-light;
    color: @blue-green-light;
  }
}

.btn-danger,
a.btn-danger {
  background-color: @brand-coral;
  color: @white-color;

  &:hover,
  &:focus,
  &:active {
    background-color: @brand-orange;
  }
}

.btn-chart,
a.btn-chart {
  font-weight: normal;
  background-color: @white-color;
  border-color: fade(@purple-dark, 15%);
  color: @purple-dark;

  &:hover,
  &:focus,
  &:active {
    background-color: fade(@blue-green-light, 50%);
    border-color: fade(@purple-dark, 25%);
  }
}

.btn-chart-control,
a.btn-chart-control {
  font-weight: normal;
  background-color: @light-blue;
  color: @purple-dark;
  border: none;
  margin: 0 @spacing-tiny;

  &:hover,
  &:focus,
  &:active {
    background-color: fade(@blue-green-light, 50%);
  }

  &.active {
    background-color: @gray-dark;
    color: @white-color;
  }
}
