/**
 * Copyright (c) 2014, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 */

// Colors
// ====================================

@gray-darkest: #636363; // Hover state for links, buttons
@gray-darker: #6d6d6d; // Text on white background
@gray-dark: #979797; // Nav links, buttons, tabs
@gray: #cccccc; // Container on gray background
@gray-light: #e6e6e5; // Container on white background
@gray-lighter: #f6f6f6; // Background
@white-color: #ffffff;
@black-color: #000;

@purple: #627cff;
@purple-pale: #dcdff9;
@purple-light: rgba(98, 124, 255, 0.2); // button disabled
@purple-medium: @purple;
@purple-dark: #281946;

@purple-blip: #4e4490;
@purple-uploader: @purple-dark;

@purple-success: #7950ea;
@orange-alert: #ffa70b;
@red-error: #de514b;

@blue-primary: #f0f5ff; // text on dark background
@blue-green: #0b9eb3;
@blue-green-light: #12d3d8;
@blue-gray-dark: #4F6A92;
@blue-gray-dark-active: #375178;
@owned-account: #b1eaf2;
@owned-account-hover: #81d7e4;

@gray-text: #545454;
@gray-background: #f2f2f2;
@gray-title: #808080;
@gray-rule: #d5d5d5;

@light-blue: #eef5f9; // Fill on chart buttons

// Brand Colors
@brand-blue: #00ebfa;
@brand-green: #2ce993;
@brand-orange: #ff5f37;
@brand-coral: #ff6f69;
@brand-peach: #ffcf9f;

// Modal Day
@weekday: #3d9998;
@weekend: #5de8de;

@monday: @weekday;
@tuesday: @weekday;
@wednesday: @weekday;
@thursday: @weekday;
@friday: @weekday;
@saturday: @weekend;
@sunday: @weekend;

// Scaffolding
// ====================================

@body-bg: @gray-lighter;
@text-color: @gray-darker;

@link-color: @blue-green;
@link-hover-color: @blue-green-light;

@link-alternative-color: @purple-medium;
@link-alternative-hover-color: @purple-dark;

// Typography
// ====================================

@font-family-base:       "Basis", "Helvetica Neue", Helvetica, Arial, sans-serif;
@font-family-mono:       "Basis Mono", "Andale Mono", monospace;
@font-size-small:        14px;
@font-size-base:         16px;
@font-size-large:        24px;
@font-size-small:        14px;
@font-size-tiny:         12px;
@line-height-base:       20px;

// Spacing
// ====================================

@spacing-large: 40px;
@spacing-base: 20px;
@spacing-medium: 15px;
@spacing-small: 10px;
@spacing-tiny: 5px;

// Notification states
// ====================================

@state-success-text: @gray-darker;
@state-success-bg: #fff;
@state-success-border: @purple-success;

@state-alert-text: @gray-darker;
@state-alert-bg: #fff;
@state-alert-border: @orange-alert;

@state-error-text: @gray-darker;
@state-error-bg: #fff;
@state-error-border: @red-error;

// Patient data layout
// ====================================

@patient-data-chart-height: 500px;
@patient-data-sidebar-bp: 980px;
@patient-data-sidebar-width: 340px;

// Media queries breakpoints
// ====================================

// Medium screen / tablet
@screen-md-min: 512px;

// Medium/Large screen
@screen-md-lg-min: 768px;

// Large screen / desktop
@screen-lg-min: 1280px;

// So media queries don't overlap when required, provide a maximum
@screen-sm-max: (@screen-md-min - 1);
@screen-md-max: (@screen-lg-min - 1);

// Z-index master list
// ====================================

@zindex-notification: 1030;
@zindex-overlay-background: 1040;
@zindex-overlay: 1050;
