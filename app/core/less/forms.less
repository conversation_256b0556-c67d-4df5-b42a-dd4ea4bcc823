/**
 * Copyright (c) 2014, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERC<PERSON>NTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 */

// Non-controls
// ====================================

label,
.form-label {
  display: block;
}

// Help text under form controls
.form-help-block {
  margin-top: @spacing-tiny;
}

// Form controls
// ====================================

@form-control-border-size:   1px;
@form-control-border-color:  @gray-dark;
@form-control-height:        (2*@line-height-base);

.form-control-border {
  border: @form-control-border-size solid fade(@form-control-border-color, 50%);
  border-radius: 0px;

  &:hover {
    border-color: @purple;
  }

  &:focus {
    // Disable default WebKit focus style
    outline: 0;
    border-color: @purple;
  }

  &:disabled {
    border-color: fade(@form-control-border-color, 70%);
    cursor: not-allowed;
  }
}

.form-control {
  display: block;
  width: 100%;

  font-size: @font-size-base;
  height: @form-control-height;
  line-height: @line-height-base;
  vertical-align: middle;

  padding: ((@form-control-height - @line-height-base)/2 - @form-control-border-size) @spacing-tiny;

  border: @form-control-border-size solid @form-control-border-color;
  border-radius: 0px;
  background: #fff;
  color: #000;

  // Remove inset shadow in iOS
  -webkit-appearance: none;

  &:hover {
    border-color: @purple;
  }

  &:focus {
    // Disable default WebKit focus style
    outline: 0;

    border-color: @purple;
  }

  &:disabled {
    border-color: lighten(@form-control-border-color, 15%);
    cursor: not-allowed;
    color: black;
  }
  // Reset height for `textarea`s
  textarea& {
    height: auto;
  }

  &::placeholder {
    font-size: @font-size-small;
  }
}

// Special styles for iOS date input
//
// In Mobile Safari, date inputs require a pixel line-height that matches the
// given height of the input.
input[type="date"] {
  line-height: @form-control-height;
  padding-top: 0;
  padding-bottom: 0;
}

select.form-control {
  -webkit-appearance: menulist;
}

// Increased accessibility of checkboxes by emphasizing actionable items
input[type="checkbox"] {
  cursor: pointer;
}

// Form groups
// ====================================
// Designed to help with the organization and spacing of vertical forms.

.form-group {
  margin-bottom: @spacing-medium;
  @media(max-width: @screen-md-max) {
    min-width: 275px;
  }
}
