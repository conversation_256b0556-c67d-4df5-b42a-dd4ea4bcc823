/**
 * Copyright (c) 2014, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHA<PERSON>ABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 */

// Reset box-sizing
// ====================================

* {
  .box-sizing(border-box);
}
*:before,
*:after {
  .box-sizing(border-box);
}


// Body reset
// ====================================

html {
  // Remove the dark touch outlines on links
  -webkit-tap-highlight-color: rgba(0,0,0,0);
}

body {
  font-family: @font-family-base;
  font-size: @font-size-base;
  line-height: @line-height-base;
  color: @text-color;
  background-color: @body-bg;
}

// Make form elements use body font styles
input,
button,
select,
textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

// Clear
// ====================================
.clear {
  clear: both;
}

// Links
// ====================================

a {
  color: @link-color;
  text-decoration: none;

  &:hover,
  &:focus {
    color: @link-hover-color;
    text-decoration: underline;
  }

  &.static:hover,
  &.static:focus {
    color: inherit;
    text-decoration: inherit;
  }
}

.link-secondary,
a.link-secondary {
  color: @gray-dark;
  text-decoration: none;

  &:hover,
  &:focus {
    color: @blue-green-light;
    text-decoration: underline;
  }
}

.link-textBlock,
a.link-textBlock {
  color: @purple;
  text-decoration: none;
  cursor: pointer;

  &:hover,
  &:focus {
    color: @purple-dark;
    text-decoration: none;
  }
}

// Images
// ====================================
img {
  vertical-align: middle;
}

// Print section
// ====================================
@media print {
  body {
    background: transparent;
  }

  .large-one-third {
    width: 33.333% !important;
  }
  .large-three-eighths {
    width: 37.5% !important;
  }
  .large-one-quarter {
    width: 25% !important;
  }
  .large-one-half {
    width: 50% !important;
  }

  .patient-data-subnav > div:first-child > div {
    display: none;
  }

  .patient-data-subnav > div:last-child > div {
    display: none;
  }
}
