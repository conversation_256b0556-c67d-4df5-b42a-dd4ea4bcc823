/**
 * Copyright (c) 2014, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHA<PERSON><PERSON>ILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 */

// List groups
// ====================================

// Parent
// Use on <ul>, <ol>, or <div>
.list-group {
  // Override default ul/ol
  margin: 0;
  padding: 0;
  list-style: none;
  padding-bottom: @spacing-large;
}

// Individual list items
// Use on <li> or <div> within the `.list-group` parent
.list-group-item {
  display: block;
}

// List item that's also a link
// Use on <a> within the `.list-group-item` parent
.list-group-item-link {
  display: block;

  // Override default link styles
  &,
  &:hover,
  &:focus {
    color: @text-color;
    text-decoration: none;
  }
}
