/**
 * Copyright (c) 2014, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 */

@font-face {
  font-family: 'Blip Icons';
  src: url('../fonts/blip-icons.eot');
  src: url('../fonts/blip-icons.eot#iefix') format('embedded-opentype'),
       url('../fonts/blip-icons.woff') format('woff'),
       url('../fonts/blip-icons.ttf') format('truetype'),
       url('../fonts/blip-icons.svg#fontello') format('svg');
  font-weight: normal;
  font-style: normal;
}

@font-face {
    font-family: 'Basis';
    src: url('../fonts/basis/basis-grotesque-light.woff') format('woff');
    font-style: normal;
    font-weight: 300;
}
@font-face {
    font-family: 'Basis';
    src: url('../fonts/basis/basis-grotesque-light-italic.woff') format('woff');
    font-style: italic;
    font-weight: 300;
}
@font-face {
    font-family: 'Basis';
    src: url('../fonts/basis/basis-grotesque-regular.woff') format('woff');
    font-style: normal;
    font-weight: 400;
}
@font-face {
    font-family: 'Basis';
    src: url('../fonts/basis/basis-grotesque-italic.woff') format('woff');
    font-style: italic;
    font-weight: 400;
}
@font-face {
    font-family: 'Basis';
    src: url('../fonts/basis/basis-grotesque-medium.woff') format('woff');
    font-style: normal;
    font-weight: 500;
}
@font-face {
    font-family: 'Basis';
    src: url('../fonts/basis/basis-grotesque-medium-italic.woff') format('woff');
    font-style: italic;
    font-weight: 500;
}
@font-face {
    font-family: 'Basis';
    src: url('../fonts/basis/basis-grotesque-bold.woff') format('woff');
    font-style: normal;
    font-weight: 700;
}
@font-face {
    font-family: 'Basis';
    src: url('../fonts/basis/basis-grotesque-bold-italic.woff') format('woff');
    font-style: italic;
    font-weight: 700;
}
@font-face {
    font-family: 'Basis';
    src: url('../fonts/basis/basis-grotesque-black.woff') format('woff');
    font-style: normal;
    font-weight: 900;
}
@font-face {
    font-family: 'Basis';
    src: url('../fonts/basis/basis-grotesque-black-italic.woff') format('woff');
    font-style: italic;
    font-weight: 900;
}
@font-face {
    font-family: 'Basis Mono';
    src: url('../fonts/basis/basis-grotesque-mono-regular.woff') format('woff');
    font-style: normal;
    font-weight: 400;
}
@font-face {
    font-family: 'Basis Mono';
    src: url('../fonts/basis/basis-grotesque-mono-italic.woff') format('woff');
    font-style: italic;
    font-weight: 400;
}
@font-face {
    font-family: 'Basis Mono';
    src: url('../fonts/basis/basis-grotesque-mono-bold.woff') format('woff');
    font-style: normal;
    font-weight: 700;
}
@font-face {
    font-family: 'Basis Mono';
    src: url('../fonts/basis/basis-grotesque-mono-bold-italic.woff') format('woff');
    font-style: italic;
    font-weight: 700;
}
