/**
 * Copyright (c) 2014, Tidepool Project
 * 
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 * 
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERC<PERSON>NTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 * 
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 */

// Borrowed from the inuit.css framework:
// https://github.com/peterwilsoncc/inuit.css/blob/LESS/generic/widths.less
// https://github.com/peterwilsoncc/inuit.css/blob/LESS/objects/grids.less

// Simple, human-readable responsive widths
// Uses inline-block (vs floats)
// Mobile-first
// No padding or gutters by default
// Example:
// 
// <div class="grid">
//   <div class="grid-item one-whole medium-one-half">Foo</div><!--
//   --><div class="grid-item one-whole medium-one-half">Bar</div>
// </div>
// 
// Note: With inline-block, you can't have white space between grid items,
// thus the comment blocks (<!-- -->)

// Grid
// ====================================

.grid {
  // Override default ul/ol if used on those element
  margin: 0;
  padding: 0;
  list-style: none;

  // Add negative left-margin to your own subclass
  // if you have grid items with padding to negate first-child's padding
  margin-left: 0;
}

.grid-item {
  display: inline-block;
  width: 100%;

  // Important:; by default goes on the bottom and will make grid items
  // not align properly
  vertical-align: top;

  // Add left padding to your own subclass if you'd like
  padding-left: 0;
}

// Widths
// ====================================

.grid-setup(@prefix: 'null') {
  // no access to outer scope
  @p: @prefix;
  @the-prefix: ~`(this.p.toJS() == "'null'")?'':this.p.toJS()`;

  /**
   * Whole
   */
  .@{the-prefix}one-whole {
    width: 100%;
  }

  /**
   * Halves
   */
  .@{the-prefix}one-half, .@{the-prefix}two-quarters, .@{the-prefix}three-sixths, .@{the-prefix}four-eighths, .@{the-prefix}five-tenths, .@{the-prefix}six-twelfths {
    width: 50%;
  }

  /**
   * Thirds
   */
  .@{the-prefix}one-third, .@{the-prefix}two-sixths, .@{the-prefix}four-twelfths {
    width: 33.333%;
  }

  .@{the-prefix}two-thirds, .@{the-prefix}four-sixths, .@{the-prefix}eight-twelfths {
    width: 66.666%;
  }

  /**
   * Quarters
   */
  .@{the-prefix}one-quarter, .@{the-prefix}two-eighths, .@{the-prefix}three-twelfths {
    width: 25%;
  }

  .@{the-prefix}three-quarters, .@{the-prefix}six-eighths, .@{the-prefix}nine-twelfths {
    width: 75%;
  }

  /**
   * Fifths
   */
  .@{the-prefix}one-fifth, .@{the-prefix}two-tenths {
    width: 20%;
  }

  .@{the-prefix}two-fifths, .@{the-prefix}four-tenths {
    width: 40%;
  }

  .@{the-prefix}three-fifths, .@{the-prefix}six-tenths {
    width: 60%;
  }

  .@{the-prefix}four-fifths, .@{the-prefix}eight-tenths {
    width: 80%;
  }

  /**
   * Sixths
   */
  .@{the-prefix}one-sixth, .@{the-prefix}two-twelfths {
    width: 16.666%;
  }

  .@{the-prefix}five-sixths, .@{the-prefix}ten-twelfths {
    width: 83.333%;
  }

  /**
   * Eighths
   */
  .@{the-prefix}one-eighth {
    width: 12.5%;
  }

  .@{the-prefix}three-eighths {
    width: 37.5%;
  }

  .@{the-prefix}five-eighths {
    width: 62.5%;
  }

  .@{the-prefix}seven-eighths {
    width: 87.5%;
  }

  /**
   * Tenths
   */
  .@{the-prefix}one-tenth {
    width: 10%;
  }

  .@{the-prefix}three-tenths {
    width: 30%;
  }

  .@{the-prefix}seven-tenths {
    width: 70%;
  }

  .@{the-prefix}nine-tenths {
    width: 90%;
  }

  /**
   * Twelfths
   */
  .@{the-prefix}one-twelfth {
    width: 8.333%;
  }

  .@{the-prefix}five-twelfths {
    width: 41.666%;
  }

  .@{the-prefix}seven-twelfths {
    width: 58.333%;
  }

  .@{the-prefix}eleven-twelfths {
    width: 91.666%;
  }
}

.grid-setup();

.responsive-widths() {
  @media (min-width: @screen-md-min) { /* tablet and up */
    .grid-setup(medium-);
  }

  @media (min-width: @screen-lg-min) { /* desktop and up */
    .grid-setup(large-);
  }
}

.responsive-widths();
