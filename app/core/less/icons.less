/**
 * Copyright (c) 2014, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of ME<PERSON><PERSON>NTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 */

// Icons generated with fontello.com

[class^="icon-"]:before, [class*=" icon-"]:before {
  font-family: "Blip Icons";
  font-style: normal;
  font-weight: normal;
  speak: none;

  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  margin-right: .2em;
  text-align: center;
  /* opacity: .8; */

  /* For safety - reset parent styles, that can break glyph codes*/
  font-variant: normal;
  text-transform: none;

  /* fix buttons height, for twitter bootstrap */
  line-height: 1em;

  /* Animation center compensation - margins should be symmetric */
  /* remove if not needed */
  margin-left: .2em;

  /* you can be more comfortable with increased icons size */
  font-size: 110%;

  /* Uncomment for 3D effect */
  /* text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3); */
}

.icon-close:before { content: '\e800'; } /* '' */
.icon-account--down:before { content: '\e801'; } /* '' */
.icon-account--up:before { content: '\e802'; } /* '' */
.icon-add:before { content: '\e803'; } /* '' */
.icon-arrow-down:before { content: '\e804'; } /* '' */
.icon-arrow-up:before { content: '\e805'; } /* '' */
.icon-back-down:before { content: '\e806'; } /* '' */
.icon-back:before { content: '\e807'; } /* '' */
.icon-delete:before { content: '\e808'; } /* '' */
.icon-down:before { content: '\e809'; } /* '' */
.icon-face-standin:before { content: '\e80b'; } /* '' */
.icon-logout:before { content: '\e80c'; } /* '' */
.icon-most-recent-up:before { content: '\e80d'; } /* '' */
.icon-pending-invite:before { content: '\e80e'; } /* '' */
.icon-permissions-own:before { content: '\e80f'; } /* '' */
.icon-permissions-upload:before { content: '\e810'; } /* '' */
.icon-permissions-view:before { content: '\e811'; } /* '' */
.icon-profile-face-2:before { content: '\e812'; } /* '' */
.icon-profile:before { content: '\e813'; } /* '' */
.icon-refresh:before { content: '\e814'; } /* '' */
.icon-remove:before { content: '\e815'; } /* '' */
.icon-right:before { content: '\e816'; } /* '' */
.icon-settings:before { content: '\e817'; } /* '' */
.icon-share-data:before { content: '\e818'; } /* '' */
.icon-unsure-data:before { content: '\e819'; } /* '' */
.icon-up:before { content: '\e81a'; } /* '' */
.icon-upload-data:before { content: '\e81b'; } /* '' */
.icon-upload:before { content: '\e81c'; } /* '' */
.icon-careteam:before { content: '\e81d'; } /* '' */
.icon-most-recent:before { content: '\e81e'; } /* '' */
.icon-new-hover:before { content: '\e81f'; } /* '' */
.icon-next-up:before { content: '\e820'; } /* '' */
.icon-next:before { content: '\e821'; } /* '' */
.icon-note-placed:before { content: '\e822'; } /* '' */

.icon-account--down {width: 20px;}

@media print {
  .icon-back, .icon-next, .icon-next-up, .icon-back-down {
    display: none;
  }
}
