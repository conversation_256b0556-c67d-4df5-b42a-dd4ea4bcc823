/**
 * Copyright (c) 2014, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERC<PERSON><PERSON>ABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 */

#app > p.loading {
  width: 50%;
  margin: 200px auto 0px;
  text-align: center;
}

// Containers control the layout of a full page width block
// They are used as two nested <div> elements, an "outer" and an "inner"
// Example:
// <div class="container-foo-outer">
//   <div class="container-foo-inner">Content goes here</div>
// </div>

// Container Nav
// ====================================

.container-nav-outer {
  margin-right: auto;
  margin-left: auto;

  @media(min-width: @screen-lg-min) {
    width: @screen-lg-min;
    padding-left: @spacing-base;
    padding-right: @spacing-base;
  }
}

.container-nav-inner {
  padding-left: ~"calc((100% - 275px) / 2)";
  padding-right: ~"calc((100% - 275px) / 2)";

  @media(min-width: @screen-lg-min) {
    padding-left: 0;
    padding-right: 0;
  }
}

// Container Box
// ====================================

.container-box-outer {
  &:extend(.container-nav-outer all);

  max-width: 100%;
  width: 100%;

  @media(min-width: @screen-md-min) {
    padding-left: @spacing-base;
    padding-right: @spacing-base;
  }

  @media(min-width: @screen-lg-min) {
    width: @screen-lg-min;
    padding-left: 0;
    padding-right: 0;
  }
}

.container-box-inner {
  padding-left: @spacing-small;
  padding-right: @spacing-small;
}

// Container Small
// ====================================

@container-small-outer-padding:  @spacing-large;
@container-small-inner-padding:  90px;
@container-small-inner-width:    320px + 2*@container-small-inner-padding;
@container-small-breakpoint:     (2*@container-small-outer-padding + @container-small-inner-width);

.container-small-outer {
  margin-right: auto;
  margin-left: auto;

  @media(min-width: @container-small-breakpoint) {
    width: @container-small-breakpoint;
    padding-left: @container-small-outer-padding;
    padding-right: @container-small-outer-padding;
  }
}

.container-small-inner {
  padding-left: @spacing-small;
  padding-right: @spacing-small;

  @media(min-width: @container-small-breakpoint) {
    width: @container-small-inner-width;
    padding-left: @container-small-inner-padding;
    padding-right: @container-small-inner-padding;
  }
}
