/* global jest, beforeEach, afterEach, test, expect, describe, it */

import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import configureStore from 'redux-mock-store';
import { Provider } from 'react-redux';
import { MemoryRouter, Route, Switch } from 'react-router-dom';
import thunk from 'redux-thunk';
import mockLocalStorage from '../../../../utils/mockLocalStorage';

// Import the mocked component
import SelectTags from '../../../../../app/components/clinic/PatientForm/SelectTags';

  const storeFixture = {
    blip: {
      loggedInUserId: 'abcd-1234',
      membershipInOtherCareTeams: [],
      selectedClinicId: '4b68d',
      clinics: {
        '4b68d': {
          id: '4b68d',
          name: 'Test Clinic',
          patientTags: [
            { id: 'id-for-alpha', name: '<PERSON>' },
            { id: 'id-for-bravo', name: '<PERSON>' },
            { id: 'id-for-charlie', name: '<PERSON>' },
          ],
        },
      },
    },
  };

describe('SelectTags', ()  => {
  const storeFixture = {
    blip: {
      loggedInUserId: 'abcd-1234',
      membershipInOtherCareTeams: [],
      selectedClinicId: '4b68d',
      clinics: {
        '4b68d': {
          id: '4b68d',
          name: 'Test Clinic',
          patientTags: [
            { id: 'id-for-alpha', name: 'Alpha' },
            { id: 'id-for-bravo', name: 'Bravo' },
            { id: 'id-for-charlie', name: 'Charlie' },
          ],
        },
      },
    },
  };

  const mockStore = configureStore([thunk]);
  let store = mockStore(storeFixture);

  it('Should suggest any tags that are in active filters', async () => {
    mockLocalStorage({
      'activePatientFilters/abcd-1234/4b68d': JSON.stringify({
        timeCGMUsePercent: null,
        lastData: null,
        lastDataType: null,
        timeInRange: [],
        meetsGlycemicTargets: true,
        patientTags: ['id-for-alpha', 'id-for-charlie'],
      }),
    });

    const testProps = {
      currentTagIds: [],
      onChange: jest.fn(),
    };

    render(
      <Provider store={store}>
        <MemoryRouter initialEntries={['/clinic-workspace']}>
          <Switch>
            <Route path='/clinic-workspace'>
              <SelectTags {...testProps} />
            </Route>
          </Switch>
        </MemoryRouter>
      </Provider>
    );

    // Open the dropdown to see the suggested tags
    const selectInput = screen.getByRole('combobox');
    await userEvent.click(selectInput);

    expect(screen.getByText('Bravo')).toBeInTheDocument();
    expect(screen.getByText('Suggested - based on current dashboard filters')).toBeInTheDocument();
    expect(screen.getByText('Alpha')).toBeInTheDocument();
    expect(screen.getByText('Charlie')).toBeInTheDocument();
  });

  it('Should fire the onChange handler with all of the selected tags', async () => {
    mockLocalStorage({
      'activePatientFilters/abcd-1234/4b68d': JSON.stringify({
        timeCGMUsePercent: null,
        lastData: null,
        lastDataType: null,
        timeInRange: [],
        meetsGlycemicTargets: true,
        patientTags: [],
      }),
    });

    const testProps = {
      currentTagIds: [],
      onChange: jest.fn(),
    };

    render(
      <Provider store={store}>
        <MemoryRouter initialEntries={['/clinic-workspace']}>
          <Switch>
            <Route path='/clinic-workspace'>
              <SelectTags {...testProps} />
            </Route>
          </Switch>
        </MemoryRouter>
      </Provider>
    );

    expect(screen.queryByText('Alpha')).not.toBeInTheDocument();

    // Open the dropdown to see the suggested tags
    const selectInput = screen.getByRole('combobox');
    await userEvent.click(selectInput);

    expect(screen.getByText('Alpha')).toBeInTheDocument();
    expect(screen.getByText('Bravo')).toBeInTheDocument();
    expect(screen.getByText('Charlie')).toBeInTheDocument();

    await userEvent.click(screen.getByText('Bravo'));
    expect(testProps.onChange).toHaveBeenCalledWith(['id-for-bravo']);
  });
});
