/* global jest, beforeEach, afterEach, test, expect, describe, it */

import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import configureStore from 'redux-mock-store';
import { Provider } from 'react-redux';
import { MemoryRouter, Route, Switch } from 'react-router-dom';
import thunk from 'redux-thunk';
import mockLocalStorage from '../../../../utils/mockLocalStorage';

// Import the mocked component
import SelectTags from '../../../../../app/components/clinic/PatientForm/SelectTags';

describe.only('SelectTags', ()  => {
  const trackMetric = jest.fn();
  // const onUpdateDataDonationAccounts = jest.fn();

  mockLocalStorage({
    'activePatientFilters/abcd-1234/4b68d': JSON.stringify({
      timeCGMUsePercent: null,
      lastData: null,
      lastDataType: null,
      timeInRange: [],
      meetsGlycemicTargets: true,
      patientTags: ['tag1', 'tag2'], // Mock some active filter tags
    }),
  });

  const storeFixture = {
    blip: {
      loggedInUserId: 'abcd-1234',
      membershipInOtherCareTeams: [],
      selectedClinicId: '4b68d', // Second Clinic
      clinics: {
        '4b68d': {
          id: '4b68d',
          name: 'Test Clinic',
          patientTags: [
            { id: 'tag1', name: 'Diabetes' },
            { id: 'tag2', name: 'Hypertension' },
            { id: 'tag3', name: 'Obesity' },
          ],
        },
      },
    },
  };

  const mockStore = configureStore([thunk]);
  let store = mockStore(storeFixture);

  // beforeEach(() => {
    // trackMetric.mockClear();
  // });

  afterEach(() => {
    // Clean up localStorage mock
    jest.restoreAllMocks();
  });

  it('should suggest tags based on active filters from localStorage', async () => {
    mockLocalStorage({
      'activePatientFilters/abcd-1234/4b68d': JSON.stringify({
        timeCGMUsePercent: null,
        lastData: null,
        lastDataType: null,
        timeInRange: [],
        meetsGlycemicTargets: true,
        patientTags: ['tag1', 'tag2'], // Mock some active filter tags
      }),
    });

    const testProps = {
      currentTagIds: [],
      onChange: jest.fn(),
    };

    render(
      <Provider store={store}>
        <MemoryRouter initialEntries={['/clinic-workspace']}>
          <Switch>
            <Route path='/clinic-workspace'>
              <SelectTags {...testProps} />
            </Route>
          </Switch>
        </MemoryRouter>
      </Provider>
    );

    // Open the dropdown to see the suggested tags
    const selectInput = screen.getByRole('combobox');
    await userEvent.click(selectInput);

    expect(screen.getByText('Suggested - based on current dashboard filters')).toBeInTheDocument();
  });
});
