/* global jest, beforeEach, afterEach, test, expect, describe, it */

import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import configureStore from 'redux-mock-store';
import { Provider } from 'react-redux';
import { MemoryRouter, Route, Switch } from 'react-router-dom';
import thunk from 'redux-thunk';
import mockLocalStorage from '../../../../utils/mockLocalStorage';
import moment from 'moment';

import ClinicPatients from '@app/pages/clinicworkspace/ClinicPatients';

describe('ClinicPatients', ()  => {
  const today = moment().toISOString();
  const yesterday = moment(today).subtract(1, 'day').toISOString();

  const hasPatientsState = merge({}, noPatientsState, {
    blip: {
      allUsersMap: {
        clinicianUserId123,
      },
      clinics: {
        clinicID123: {
          ...defaultClinic,
          clinicians:{
            clinicianUserId123,
          },
          patients: {
            patient1: {
              id: 'patient1',
              email: '<EMAIL>',
              fullName: 'Patient One',
              birthDate: '1999-01-01' ,
              permissions: { view : {} }
            },
            patient2: {
              id: 'patient2',
              email: '<EMAIL>',
              fullName: 'Patient Two',
              birthDate: '1999-02-02',
              mrn: 'MRN123',
              permissions: { custodian : {} }
            },
          },
        },
      },
    },
  });

  const tier0300ClinicState = {
    blip: {
      ...hasPatientsState.blip,
      clinics: {
        clinicID123: {
          ...hasPatientsState.blip.clinics.clinicID123,
          ...clinicUIDetails({
            ...hasPatientsState.blip.clinics.clinicID123,
            tier: 'tier0300',
          }),
          tier: 'tier0300',
          patientTags: [
            { id: 'tag3', name: 'ttest tag 3'},
            { id: 'tag2', name: 'test tag 2'},
            { id: 'tag1', name: '>test tag 1'},
          ],
          patients: {
            patient1: {
              id: 'patient1',
              email: '<EMAIL>',
              fullName: 'Patient One',
              birthDate: '1999-01-01',
              mrn: 'MRN012',
              summary: {},
              permissions: { custodian : {} },
              tags: [],
              reviews: [
                { clinicianId: 'clinicianUserId123', time: today },
                { clinicianId: 'clinicianUserId123', time: yesterday },
              ],
            },
            patient2: {
              id: 'patient2',
              email: '<EMAIL>',
              fullName: 'Patient Two',
              birthDate: '1999-02-02',
              mrn: 'MRN123',
              summary:{
                bgmStats: {
                  dates: {
                    lastData: yesterday,
                  },
                  periods: { '14d': {
                    averageGlucoseMmol: 10.5,
                    averageDailyRecords: 0.25,
                    timeInVeryLowRecords: 1,
                    timeInVeryHighRecords: 2,
                  } },
                },
                cgmStats: {
                  dates: {
                    lastData: today,
                  },
                  periods: { '14d': {
                    timeCGMUsePercent: 0.85,
                    timeCGMUseMinutes: 23 * 60,
                    glucoseManagementIndicator: 7.75,
                  } },
                },
              },
              permissions: { custodian : undefined },
              tags: ['tag1'],
              reviews: [{ clinicianId: 'clinicianUserId123', time: yesterday }],
            },
            patient3: {
              id: 'patient3',
              email: '<EMAIL>',
              fullName: 'Patient Three',
              birthDate: '1999-03-03',
              mrn: 'mrn456',
              summary: {
                bgmStats: {
                  dates: {
                    lastData: moment().subtract(1, 'day').toISOString(),
                  },
                  periods: { '14d': {
                    averageGlucoseMmol: 11.5,
                    averageDailyRecords: 1.25,
                    timeInVeryLowRecords: 3,
                    timeInVeryHighRecords: 4,
                  } },
                },
                cgmStats: {
                  dates: {
                    lastData: yesterday,
                  },
                  periods: {
                    '30d': {
                      timeCGMUsePercent: 0.70,
                      timeCGMUseMinutes:  7 * 24 * 60,
                      glucoseManagementIndicator: 7.5,
                    },
                    '14d': {
                      timeCGMUsePercent: 0.70,
                      timeCGMUseMinutes:  7 * 24 * 60,
                      glucoseManagementIndicator: 6.5,
                    },
                    '7d': {
                      timeCGMUsePercent: 0.70,
                      timeCGMUseMinutes:  7 * 24 * 60,
                      glucoseManagementIndicator: 5.5,
                    },
                    '1d': {
                      timeCGMUsePercent: 0.70,
                      timeCGMUseMinutes:  7 * 24 * 60,
                      glucoseManagementIndicator: 4.5,
                    },
                  },
                },
              },
              tags: ['tag1', 'tag2', 'tag3'],
              reviews: [{ clinicianId: 'clinicianUserId123', time: moment(today).subtract(30, 'd').toISOString() }],
            },
            patient4: {
              id: 'patient4',
              email: '<EMAIL>',
              fullName: 'Patient Four',
              birthDate: '1999-04-04',
              mrn: 'mrn789',
              summary: {
                bgmStats: {
                  dates: {
                    lastData: yesterday,
                  },
                  periods: { '14d': {
                    averageGlucoseMmol: 12.5,
                    averageDailyRecords: 1.5,
                    timeInVeryLowRecords: 0,
                    timeInVeryHighRecords: 0,
                  } },
                },
                cgmStats: {
                  dates: {
                    lastData: moment().subtract(30, 'days').toISOString(),
                  },
                  periods: { '14d': {
                    timeCGMUsePercent: 0.69,
                    timeCGMUseMinutes:  7 * 24 * 60,
                    glucoseManagementIndicator: 8.5,
                  } },
                },
              },
              reviews: [{ clinicianId: 'clinicianUserId123', time: moment('2024-03-05T12:00:00.000Z').toISOString() }],
            },
            patient5: {
              id: 'patient5',
              email: '<EMAIL>',
              fullName: 'Patient Five',
              birthDate: '1999-05-05',
              mrn: 'mrn101',
              summary: {
                cgmStats: {
                  dates: {
                    lastData: moment().subtract(31, 'days').toISOString(),
                  },
                  periods: { '14d': {
                    timeCGMUsePercent: 0.69,
                    timeCGMUseMinutes:  30 * 24 * 60,
                    glucoseManagementIndicator: 8.5,
                  } },
                },
              },
            },
          },
        },
      },
    },
  };

  let defaultProps = {
    trackMetric: jest.fn(),
    t: jest.fn(),
    searchDebounceMs: 0,
    api: {
      clinics: {
        getPatientFromClinic: jest.fn(),
        getPatientsForClinic: jest.fn(),
        deletePatientFromClinic: jest.fn(),
        createClinicCustodialAccount: jest.fn(),
        updateClinicPatient: jest.fn(),
        sendPatientUploadReminder: jest.fn(),
        sendPatientDataProviderConnectRequest: jest.fn(),
        createClinicPatientTag: jest.fn(),
        updateClinicPatientTag: jest.fn(),
        deleteClinicPatientTag: jest.fn(),
        deleteClinicPatientTag: jest.fn(),
        getPatientsForRpmReport: jest.fn(),
        setClinicPatientLastReviewed: jest.fn(),
        revertClinicPatientLastReviewed: jest.fn(),
      },
    },
  };

  const storeFixture = {
    blip: {
      loggedInUserId: 'abcd-1234',
      membershipInOtherCareTeams: [],
      selectedClinicId: '4b68d',
      clinics: {
        '4b68d': {
          id: '4b68d',
          name: 'Test Clinic',
          patientTags: [
            { id: 'id-for-delta', name: 'Delta' },
            { id: 'id-for-charlie', name: 'Charlie' },
            { id: 'id-for-bravo', name: 'Bravo' },
            { id: 'id-for-alpha', name: 'Alpha' },
          ],
        },
      },
    },
  };

  const mockStore = configureStore([thunk]);
  let store = mockStore(storeFixture);

  describe('has patients', () => {
    describe('show names clicked', () => {
      describe('tier0300 clinic', () => {
        beforeEach(() => {
          store = mockStore(tier0300ClinicState);
        });

        describe('managing patient tags', () => {

          it('', () => {
            render(
              <Provider store={store}>
                <MemoryRouter initialEntries={['/clinic-workspace']}>
                  <Switch>
                    <Route path='/clinic-workspace'>
                      <ClinicPatients {...defaultProps} />
                    </Route>
                  </Switch>
                </MemoryRouter>
              </Provider>
            );

            console.log(screen.debug());

            expect(true).toEqual(false);
          });
        });
      });
    });
  });

  // it('', async () => {
  //   mockLocalStorage({
  //     'activePatientFilters/abcd-1234/4b68d': JSON.stringify({
  //       timeCGMUsePercent: null,
  //       lastData: null,
  //       lastDataType: null,
  //       timeInRange: [],
  //       meetsGlycemicTargets: true,
  //       patientTags: ['id-for-alpha', 'id-for-charlie'],
  //     }),
  //   });

  //   const testProps = {
  //     currentTagIds: ['id-for-delta'], // Patient currently has Delta tag
  //     onChange: jest.fn(),
  //   };

  //   render(
  //     <Provider store={store}>
  //       <MemoryRouter initialEntries={['/clinic-workspace']}>
  //         <Switch>
  //           <Route path='/clinic-workspace'>
  //             <SelectTags {...testProps} />
  //           </Route>
  //         </Switch>
  //       </MemoryRouter>
  //     </Provider>
  //   );

  //   // Current tags of patient should be shown
  //   expect(screen.getByText('Delta')).toBeInTheDocument();

  //   // Tag options should not be shown
  //   expect(screen.queryByText('Alpha')).not.toBeInTheDocument();
  //   expect(screen.queryByText('Bravo')).not.toBeInTheDocument();
  //   expect(screen.queryByText('Charlie')).not.toBeInTheDocument();

  //   // Open the dropdown to see the suggested tags
  //   const selectInput = screen.getByRole('combobox');
  //   await userEvent.click(selectInput);

  //   // Tags options are now visible
  //   expect(screen.getByText('Alpha')).toBeInTheDocument();
  //   expect(screen.getByText('Bravo')).toBeInTheDocument();
  //   expect(screen.getByText('Charlie')).toBeInTheDocument();
  //   expect(screen.getByText('Delta')).toBeInTheDocument();

  //   // Suggested tags should be shown before non-suggesteds. Suggested tags are the ones in active filters.
  //   // Order should be Alpha, Charlie, Bravo
  //   const suggestedHeader = screen.getByText('Suggested - based on current dashboard filters');
  //   const tagAlpha = screen.getByText('Alpha');
  //   const tagBravo = screen.getByText('Bravo');
  //   const tagCharlie = screen.getByText('Charlie');

  //   expect(suggestedHeader.compareDocumentPosition(tagAlpha)).toEqual(Node.DOCUMENT_POSITION_FOLLOWING);
  //   expect(tagAlpha.compareDocumentPosition(tagCharlie)).toEqual(Node.DOCUMENT_POSITION_FOLLOWING);
  //   expect(tagCharlie.compareDocumentPosition(tagBravo)).toEqual(Node.DOCUMENT_POSITION_FOLLOWING);

  //   // Clicking on a Tag fires the onChange handler with the clicked tag
  //   await userEvent.click(screen.getByText('Bravo'));
  //   expect(testProps.onChange).toHaveBeenCalledWith([
  //     'id-for-delta', // Patient's current tags
  //     'id-for-bravo', // New tag that has been selected
  //   ]);
  // });
});
