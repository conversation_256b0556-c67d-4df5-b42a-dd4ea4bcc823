## Blip's usage of Redux

As of the spring of 2016, we have migrated the application state management in blip to [Redux](http://redux.js.org/). Redux is a lightweight but powerful state container for JavaScript applications that takes inspiration equally from (a) Facebook's [Flux](https://facebook.github.io/flux/) application architecture (especially its emphasis on one-way data flow) and (b) functional programming, in particular [Elm](http://elm-lang.org/), a functional programming language for building GUIs on the web.

Please read the general [Redux @ Tidepool](http://developer.tidepool.io/docs/front-end/redux/index.html 'Tidepool developer portal: Redux @ Tidepool') documentation before reading the documents listed below giving details of our usage of Redux in the Tidepool uploader.

Detailed documentation on the uploader's Redux implementation:

<!-- TODO: add example state tree -->
- [Glossary of state tree terms](StateTreeGlossary.md)
