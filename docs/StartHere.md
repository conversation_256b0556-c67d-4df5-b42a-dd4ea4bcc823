# Blip developer guide

Blip is a single-page application built using [React](https://facebook.github.io/react/ 'React') - a JavaScript library for building user interfaces.

The root-level [README](../README.md) contains the nuts & bolts of installing, configuring, and commands to accomplish various tasks such as running the tests and testing the production build.

As you're getting ready to develop code in this repository, we recommend starting with the following documents:

- [overview of features](./FeatureOverview.md)
- [app & directory structure](./DirectoryStructure.md)
- [architecture](./Architecture.md)
- [code style](./CodeStyle.md)
- [dependencies](./Dependencies.md)
