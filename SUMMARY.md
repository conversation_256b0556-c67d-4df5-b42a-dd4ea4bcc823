# Summary

+ [Blip developer guide](docs/StartHere.md)
    + [overview of features](docs/FeatureOverview.md)
    + [app & directory structure](docs/DirectoryStructure.md)
    + [architecture](docs/Architecture.md)
    + [code style](docs/CodeStyle.md)
+ [usage of dependencies](docs/Dependencies.md)
    + [React](docs/React.md)
    + [React Router](docs/ReactRouter.md)
    + [Redux](docs/Redux.md)
        + [Glossary of state tree terms](docs/StateTreeGlossary.md)
    + [webpack](docs/Webpack.md)
+ [misc]
    + ["fake child accounts"](docs/FakeChildAccounts.md)
