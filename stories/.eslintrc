{
  "extends": "airbnb",
  "parser": "babel-eslint",
  "plugins": ["lodash"],
  "rules": {
    "arrow-parens": 0,
    "class-methods-use-this": 0,
    "comma-dangle": ["error", {
      "arrays": "only-multiline",
      "objects": "only-multiline",
      "imports": "only-multiline",
      "exports": "only-multiline",
      "functions": "never"
    }],
    "function-paren-newline": 0,
    "implicit-arrow-linebreak": 0,
    "import/extensions": 0,
    "import/first": 0,
    "import/newline-after-import": 0,
    "import/no-cycle": 0,
    "import/no-extraneous-dependencies": 0,
    "import/no-named-as-default": 0,
    "import/no-named-as-default-member": 0,
    "import/no-useless-path-segments": 0,
    "import/order": 0,
    "import/prefer-default-export": 0,
    "jsx-a11y/alt-text": 0,
    "jsx-a11y/click-events-have-key-events": 0,
    "jsx-a11y/label-has-for": 0,
    "jsx-a11y/mouse-events-have-key-events": 0,
    "jsx-a11y/no-noninteractive-element-interactions": 0,
    "jsx-a11y/no-static-element-interactions": 0,
    "lodash/prefer-lodash-method": ["error", {
      "ignoreMethods": ["split", "trim", "replace", "toLower"]
    }],
    "no-else-return": 0,
    "no-extra-boolean-cast": 0,
    "no-lonely-if": 0,
    "no-multi-assign": 0,
    "no-plusplus": 0,
    "no-promise-executor-return": 0,
    "no-prototype-builtins": 0,
    "no-restricted-globals": 0,
    "object-curly-newline": 0,
    "operator-assignment": 0,
    "operator-linebreak": 0,
    "prefer-destructuring": 0,
    "react/button-has-type": 0,
    "react/default-props-match-prop-types": 0,
    "react/destructuring-assignment": 0,
    "react/forbid-prop-types": 0,
    "react/jsx-closing-bracket-location": 0,
    "react/jsx-curly-brace-presence": 0,
    "react/jsx-filename-extension": 0,
    "react/jsx-max-props-per-line": 0,
    "react/jsx-no-bind": 0,
    "react/jsx-one-expression-per-line": 0,
    "react/jsx-wrap-multilines": 0,
    "react/no-access-state-in-setstate": 0,
    "react/no-deprecated": 0,
    "react/no-unused-prop-types": 0,
    "react/prop-types": 0,
    "react/require-default-props": 0,
    "react/sort-comp": 0,
    "space-unary-ops": 0,
    "react-hooks/rules-of-hooks": 0,
    //New Rules Per Errors -- not sure if these are the right settings for these rules
    "react/jsx-fragments": 0,
    "react/jsx-curly-newline": 0,
    "react/state-in-constructor": 0,
    "react/static-property-placement": 0,
    "react/jsx-props-no-spreading": 0
  }
}
