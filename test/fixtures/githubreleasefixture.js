const releases = [
  {
    'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/7735222',
    'assets_url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/7735222/assets',
    'upload_url': 'https://uploads.github.com/repos/tidepool-org/chrome-uploader/releases/7735222/assets{?name,label}',
    'html_url': 'https://github.com/tidepool-org/chrome-uploader/releases/tag/v2.0.2',
    'id': 7735222,
    'tag_name': 'v2.0.2',
    'target_commitish': 'master',
    'name': '2.0.2',
    'draft': false,
    'author': {
      'login': 'tidepool-org-build-uploader',
      'id': 29215410,
      'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
      'gravatar_id': '',
      'url': 'https://api.github.com/users/tidepool-org-build-uploader',
      'html_url': 'https://github.com/tidepool-org-build-uploader',
      'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
      'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
      'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
      'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
      'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
      'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
      'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
      'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
      'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
      'type': 'User',
      'site_admin': false
    },
    'prerelease': false,
    'created_at': '2017-09-15T09:18:59Z',
    'published_at': '2017-09-15T10:25:42Z',
    'assets': [
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4831362',
        'id': 4831362,
        'name': 'latest-mac.json',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/json',
        'state': 'uploaded',
        'size': 187,
        'download_count': 184,
        'created_at': '2017-09-15T09:28:15Z',
        'updated_at': '2017-09-15T09:28:15Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.2/latest-mac.json'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4831364',
        'id': 4831364,
        'name': 'latest-mac.yml',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'text/yaml',
        'state': 'uploaded',
        'size': 287,
        'download_count': 0,
        'created_at': '2017-09-15T09:28:15Z',
        'updated_at': '2017-09-15T09:28:15Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.2/latest-mac.yml'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4831480',
        'id': 4831480,
        'name': 'latest.yml',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'text/yaml',
        'state': 'uploaded',
        'size': 362,
        'download_count': 105,
        'created_at': '2017-09-15T09:35:57Z',
        'updated_at': '2017-09-15T09:35:57Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.2/latest.yml'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4831445',
        'id': 4831445,
        'name': 'tidepool-uploader-2.0.2-ia32-win.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 61424905,
        'download_count': 0,
        'created_at': '2017-09-15T09:32:58Z',
        'updated_at': '2017-09-15T09:33:03Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.2/tidepool-uploader-2.0.2-ia32-win.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4831363',
        'id': 4831363,
        'name': 'tidepool-uploader-2.0.2-mac.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 49073952,
        'download_count': 22,
        'created_at': '2017-09-15T09:28:15Z',
        'updated_at': '2017-09-15T09:28:19Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.2/tidepool-uploader-2.0.2-mac.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4831492',
        'id': 4831492,
        'name': 'tidepool-uploader-2.0.2-win.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 70443589,
        'download_count': 0,
        'created_at': '2017-09-15T09:36:41Z',
        'updated_at': '2017-09-15T09:36:50Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.2/tidepool-uploader-2.0.2-win.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4831354',
        'id': 4831354,
        'name': 'tidepool-uploader-2.0.2.dmg',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/x-apple-diskimage',
        'state': 'uploaded',
        'size': 51059994,
        'download_count': 144,
        'created_at': '2017-09-15T09:27:42Z',
        'updated_at': '2017-09-15T09:27:47Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.2/tidepool-uploader-2.0.2.dmg'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4831331',
        'id': 4831331,
        'name': 'tidepool-uploader-dev-2.0.2-mac.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 49074343,
        'download_count': 0,
        'created_at': '2017-09-15T09:25:49Z',
        'updated_at': '2017-09-15T09:25:53Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.2/tidepool-uploader-dev-2.0.2-mac.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4831323',
        'id': 4831323,
        'name': 'tidepool-uploader-dev-2.0.2.dmg',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/x-apple-diskimage',
        'state': 'uploaded',
        'size': 51035272,
        'download_count': 0,
        'created_at': '2017-09-15T09:25:17Z',
        'updated_at': '2017-09-15T09:25:21Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.2/tidepool-uploader-dev-2.0.2.dmg'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4812409',
        'id': 4812409,
        'name': 'tidepool-uploader-dev.-2.0.2-ia32-win.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 61425665,
        'download_count': 0,
        'created_at': '2017-09-13T09:34:46Z',
        'updated_at': '2017-09-13T09:34:49Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.2/tidepool-uploader-dev.-2.0.2-ia32-win.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4812436',
        'id': 4812436,
        'name': 'tidepool-uploader-dev.-2.0.2-win.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 70442722,
        'download_count': 0,
        'created_at': '2017-09-13T09:38:19Z',
        'updated_at': '2017-09-13T09:38:23Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.2/tidepool-uploader-dev.-2.0.2-win.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4812433',
        'id': 4812433,
        'name': 'tidepool-uploader-dev.-setup-2.0.2.exe',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/x-msdownload',
        'state': 'uploaded',
        'size': 85679912,
        'download_count': 0,
        'created_at': '2017-09-13T09:38:13Z',
        'updated_at': '2017-09-13T09:38:18Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.2/tidepool-uploader-dev.-setup-2.0.2.exe'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4831479',
        'id': 4831479,
        'name': 'tidepool-uploader-setup-2.0.2.exe',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/x-msdownload',
        'state': 'uploaded',
        'size': 85684072,
        'download_count': 114,
        'created_at': '2017-09-15T09:35:56Z',
        'updated_at': '2017-09-15T09:36:06Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.2/tidepool-uploader-setup-2.0.2.exe'
      }
    ],
    'tarball_url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/tarball/v2.0.2',
    'zipball_url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/zipball/v2.0.2',
    'body': 'Adds support for Dexcom G5 data on Tandem X2 pumps'
  },
  {
    'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/7688181',
    'assets_url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/7688181/assets',
    'upload_url': 'https://uploads.github.com/repos/tidepool-org/chrome-uploader/releases/7688181/assets{?name,label}',
    'html_url': 'https://github.com/tidepool-org/chrome-uploader/releases/tag/v2.0.1',
    'id': 7688181,
    'tag_name': 'v2.0.1',
    'target_commitish': 'master',
    'name': '2.0.1',
    'draft': false,
    'author': {
      'login': 'tidepool-org-build-uploader',
      'id': 29215410,
      'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
      'gravatar_id': '',
      'url': 'https://api.github.com/users/tidepool-org-build-uploader',
      'html_url': 'https://github.com/tidepool-org-build-uploader',
      'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
      'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
      'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
      'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
      'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
      'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
      'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
      'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
      'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
      'type': 'User',
      'site_admin': false
    },
    'prerelease': false,
    'created_at': '2017-09-08T20:12:10Z',
    'published_at': '2017-09-08T20:53:51Z',
    'assets': [
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4781911',
        'id': 4781911,
        'name': 'latest-mac.json',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/json',
        'state': 'uploaded',
        'size': 187,
        'download_count': 108,
        'created_at': '2017-09-08T20:21:24Z',
        'updated_at': '2017-09-08T20:21:24Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1/latest-mac.json'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4781913',
        'id': 4781913,
        'name': 'latest-mac.yml',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'text/yaml',
        'state': 'uploaded',
        'size': 287,
        'download_count': 0,
        'created_at': '2017-09-08T20:21:25Z',
        'updated_at': '2017-09-08T20:21:25Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1/latest-mac.yml'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4782008',
        'id': 4782008,
        'name': 'latest.yml',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'text/yaml',
        'state': 'uploaded',
        'size': 362,
        'download_count': 55,
        'created_at': '2017-09-08T20:47:21Z',
        'updated_at': '2017-09-08T20:47:21Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1/latest.yml'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4781986',
        'id': 4781986,
        'name': 'tidepool-uploader-2.0.1-ia32-win.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 61428984,
        'download_count': 0,
        'created_at': '2017-09-08T20:43:59Z',
        'updated_at': '2017-09-08T20:44:10Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1/tidepool-uploader-2.0.1-ia32-win.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4781912',
        'id': 4781912,
        'name': 'tidepool-uploader-2.0.1-mac.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 49073206,
        'download_count': 29,
        'created_at': '2017-09-08T20:21:24Z',
        'updated_at': '2017-09-08T20:21:27Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1/tidepool-uploader-2.0.1-mac.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4782010',
        'id': 4782010,
        'name': 'tidepool-uploader-2.0.1-win.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 70440582,
        'download_count': 1,
        'created_at': '2017-09-08T20:47:46Z',
        'updated_at': '2017-09-08T20:47:50Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1/tidepool-uploader-2.0.1-win.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4781905',
        'id': 4781905,
        'name': 'tidepool-uploader-2.0.1.dmg',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/x-apple-diskimage',
        'state': 'uploaded',
        'size': 51040135,
        'download_count': 39,
        'created_at': '2017-09-08T20:20:55Z',
        'updated_at': '2017-09-08T20:20:59Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1/tidepool-uploader-2.0.1.dmg'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4781888',
        'id': 4781888,
        'name': 'tidepool-uploader-dev-2.0.1-mac.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 49073128,
        'download_count': 0,
        'created_at': '2017-09-08T20:19:05Z',
        'updated_at': '2017-09-08T20:19:08Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1/tidepool-uploader-dev-2.0.1-mac.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4781883',
        'id': 4781883,
        'name': 'tidepool-uploader-dev-2.0.1.dmg',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/x-apple-diskimage',
        'state': 'uploaded',
        'size': 51059483,
        'download_count': 1,
        'created_at': '2017-09-08T20:18:32Z',
        'updated_at': '2017-09-08T20:18:35Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1/tidepool-uploader-dev-2.0.1.dmg'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4780753',
        'id': 4780753,
        'name': 'tidepool-uploader-dev.-2.0.1-ia32-win.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 61423964,
        'download_count': 0,
        'created_at': '2017-09-08T16:57:10Z',
        'updated_at': '2017-09-08T16:57:14Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1/tidepool-uploader-dev.-2.0.1-ia32-win.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4780779',
        'id': 4780779,
        'name': 'tidepool-uploader-dev.-2.0.1-win.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 70437707,
        'download_count': 2,
        'created_at': '2017-09-08T17:01:01Z',
        'updated_at': '2017-09-08T17:01:05Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1/tidepool-uploader-dev.-2.0.1-win.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4780773',
        'id': 4780773,
        'name': 'tidepool-uploader-dev.-setup-2.0.1.exe',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/x-msdownload',
        'state': 'uploaded',
        'size': 85667168,
        'download_count': 5,
        'created_at': '2017-09-08T17:00:19Z',
        'updated_at': '2017-09-08T17:00:22Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1/tidepool-uploader-dev.-setup-2.0.1.exe'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4782007',
        'id': 4782007,
        'name': 'tidepool-uploader-setup-2.0.1.exe',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/x-msdownload',
        'state': 'uploaded',
        'size': 85668976,
        'download_count': 68,
        'created_at': '2017-09-08T20:47:20Z',
        'updated_at': '2017-09-08T20:47:35Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1/tidepool-uploader-setup-2.0.1.exe'
      }
    ],
    'tarball_url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/tarball/v2.0.1',
    'zipball_url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/zipball/v2.0.1',
    'body': 'Fixes an issue where browser links (e.g. \'Get Support\') wouldn\'t open on 32-bit Windows'
  },
  {
    'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/7672663',
    'assets_url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/7672663/assets',
    'upload_url': 'https://uploads.github.com/repos/tidepool-org/chrome-uploader/releases/7672663/assets{?name,label}',
    'html_url': 'https://github.com/tidepool-org/chrome-uploader/releases/tag/v2.0.1-dev.3',
    'id': 7672663,
    'tag_name': 'v2.0.1-dev.3',
    'target_commitish': 'krystophv/chrome-launch',
    'name': '2.0.1-dev.3',
    'draft': false,
    'author': {
      'login': 'tidepool-org-build-uploader',
      'id': 29215410,
      'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
      'gravatar_id': '',
      'url': 'https://api.github.com/users/tidepool-org-build-uploader',
      'html_url': 'https://github.com/tidepool-org-build-uploader',
      'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
      'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
      'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
      'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
      'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
      'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
      'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
      'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
      'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
      'type': 'User',
      'site_admin': false
    },
    'prerelease': true,
    'created_at': '2017-09-07T18:55:17Z',
    'published_at': '2017-09-08T10:39:27Z',
    'assets': [
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4770763',
        'id': 4770763,
        'name': 'latest-mac.json',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/json',
        'state': 'uploaded',
        'size': 205,
        'download_count': 0,
        'created_at': '2017-09-07T19:04:28Z',
        'updated_at': '2017-09-07T19:04:28Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1-dev.3/latest-mac.json'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4770764',
        'id': 4770764,
        'name': 'latest-mac.yml',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'text/yaml',
        'state': 'uploaded',
        'size': 305,
        'download_count': 0,
        'created_at': '2017-09-07T19:04:28Z',
        'updated_at': '2017-09-07T19:04:28Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1-dev.3/latest-mac.yml'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4772244',
        'id': 4772244,
        'name': 'latest.yml',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'text/yaml',
        'state': 'uploaded',
        'size': 380,
        'download_count': 2,
        'created_at': '2017-09-07T23:17:38Z',
        'updated_at': '2017-09-07T23:17:38Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1-dev.3/latest.yml'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4772213',
        'id': 4772213,
        'name': 'tidepool-uploader-2.0.1-dev.3-ia32-win.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 61427548,
        'download_count': 0,
        'created_at': '2017-09-07T23:14:26Z',
        'updated_at': '2017-09-07T23:14:44Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1-dev.3/tidepool-uploader-2.0.1-dev.3-ia32-win.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4770762',
        'id': 4770762,
        'name': 'tidepool-uploader-2.0.1-dev.3-mac.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 49073174,
        'download_count': 0,
        'created_at': '2017-09-07T19:04:28Z',
        'updated_at': '2017-09-07T19:04:33Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1-dev.3/tidepool-uploader-2.0.1-dev.3-mac.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4772247',
        'id': 4772247,
        'name': 'tidepool-uploader-2.0.1-dev.3-win.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 70440426,
        'download_count': 0,
        'created_at': '2017-09-07T23:18:04Z',
        'updated_at': '2017-09-07T23:18:29Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1-dev.3/tidepool-uploader-2.0.1-dev.3-win.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4770758',
        'id': 4770758,
        'name': 'tidepool-uploader-2.0.1-dev.3.dmg',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/x-apple-diskimage',
        'state': 'uploaded',
        'size': 51047569,
        'download_count': 0,
        'created_at': '2017-09-07T19:03:49Z',
        'updated_at': '2017-09-07T19:03:52Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1-dev.3/tidepool-uploader-2.0.1-dev.3.dmg'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4770747',
        'id': 4770747,
        'name': 'tidepool-uploader-dev-2.0.1-dev.3-mac.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 49073348,
        'download_count': 0,
        'created_at': '2017-09-07T19:01:58Z',
        'updated_at': '2017-09-07T19:02:03Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1-dev.3/tidepool-uploader-dev-2.0.1-dev.3-mac.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4770736',
        'id': 4770736,
        'name': 'tidepool-uploader-dev-2.0.1-dev.3.dmg',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/x-apple-diskimage',
        'state': 'uploaded',
        'size': 51034814,
        'download_count': 0,
        'created_at': '2017-09-07T19:01:27Z',
        'updated_at': '2017-09-07T19:01:31Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1-dev.3/tidepool-uploader-dev-2.0.1-dev.3.dmg'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4769857',
        'id': 4769857,
        'name': 'tidepool-uploader-dev.-2.0.1-dev.3-ia32-win.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 61428188,
        'download_count': 0,
        'created_at': '2017-09-07T16:58:20Z',
        'updated_at': '2017-09-07T16:58:27Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1-dev.3/tidepool-uploader-dev.-2.0.1-dev.3-ia32-win.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4769884',
        'id': 4769884,
        'name': 'tidepool-uploader-dev.-2.0.1-dev.3-win.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 70441222,
        'download_count': 0,
        'created_at': '2017-09-07T17:02:10Z',
        'updated_at': '2017-09-07T17:02:15Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1-dev.3/tidepool-uploader-dev.-2.0.1-dev.3-win.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4769882',
        'id': 4769882,
        'name': 'tidepool-uploader-dev.-setup-2.0.1-dev.3.exe',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/x-msdownload',
        'state': 'uploaded',
        'size': 85674056,
        'download_count': 1,
        'created_at': '2017-09-07T17:01:47Z',
        'updated_at': '2017-09-07T17:01:51Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1-dev.3/tidepool-uploader-dev.-setup-2.0.1-dev.3.exe'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4772243',
        'id': 4772243,
        'name': 'tidepool-uploader-setup-2.0.1-dev.3.exe',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/x-msdownload',
        'state': 'uploaded',
        'size': 85673104,
        'download_count': 7,
        'created_at': '2017-09-07T23:17:37Z',
        'updated_at': '2017-09-07T23:17:54Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1-dev.3/tidepool-uploader-setup-2.0.1-dev.3.exe'
      }
    ],
    'tarball_url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/tarball/v2.0.1-dev.3',
    'zipball_url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/zipball/v2.0.1-dev.3',
    'body': 'Fixes an issue where browser links (e.g. \'Get Support\') wouldn\'t open on 32-bit Windows'
  },
  {
    'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/7665714',
    'assets_url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/7665714/assets',
    'upload_url': 'https://uploads.github.com/repos/tidepool-org/chrome-uploader/releases/7665714/assets{?name,label}',
    'html_url': 'https://github.com/tidepool-org/chrome-uploader/releases/tag/v2.0.1-dev.1',
    'id': 7665714,
    'tag_name': 'v2.0.1-dev.1',
    'target_commitish': 'gniezen/multiple-pid-vid',
    'name': '2.0.1-dev.1',
    'draft': false,
    'author': {
      'login': 'tidepool-org-build-uploader',
      'id': 29215410,
      'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
      'gravatar_id': '',
      'url': 'https://api.github.com/users/tidepool-org-build-uploader',
      'html_url': 'https://github.com/tidepool-org-build-uploader',
      'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
      'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
      'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
      'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
      'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
      'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
      'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
      'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
      'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
      'type': 'User',
      'site_admin': false
    },
    'prerelease': true,
    'created_at': '2017-09-07T09:15:39Z',
    'published_at': '2017-09-07T09:57:42Z',
    'assets': [
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4765455',
        'id': 4765455,
        'name': 'latest-mac.json',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/json',
        'state': 'uploaded',
        'size': 205,
        'download_count': 9,
        'created_at': '2017-09-07T09:25:23Z',
        'updated_at': '2017-09-07T09:25:23Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1-dev.1/latest-mac.json'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4765458',
        'id': 4765458,
        'name': 'latest-mac.yml',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'text/yaml',
        'state': 'uploaded',
        'size': 305,
        'download_count': 0,
        'created_at': '2017-09-07T09:25:23Z',
        'updated_at': '2017-09-07T09:25:23Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1-dev.1/latest-mac.yml'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4765558',
        'id': 4765558,
        'name': 'latest.yml',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'text/yaml',
        'state': 'uploaded',
        'size': 380,
        'download_count': 31,
        'created_at': '2017-09-07T09:36:01Z',
        'updated_at': '2017-09-07T09:36:02Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1-dev.1/latest.yml'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4765538',
        'id': 4765538,
        'name': 'tidepool-uploader-2.0.1-dev.1-ia32-win.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 61419441,
        'download_count': 0,
        'created_at': '2017-09-07T09:32:33Z',
        'updated_at': '2017-09-07T09:32:44Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1-dev.1/tidepool-uploader-2.0.1-dev.1-ia32-win.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4765456',
        'id': 4765456,
        'name': 'tidepool-uploader-2.0.1-dev.1-mac.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 49067597,
        'download_count': 2,
        'created_at': '2017-09-07T09:25:23Z',
        'updated_at': '2017-09-07T09:25:28Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1-dev.1/tidepool-uploader-2.0.1-dev.1-mac.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4765569',
        'id': 4765569,
        'name': 'tidepool-uploader-2.0.1-dev.1-win.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 70437844,
        'download_count': 0,
        'created_at': '2017-09-07T09:36:42Z',
        'updated_at': '2017-09-07T09:36:45Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1-dev.1/tidepool-uploader-2.0.1-dev.1-win.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4765445',
        'id': 4765445,
        'name': 'tidepool-uploader-2.0.1-dev.1.dmg',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/x-apple-diskimage',
        'state': 'uploaded',
        'size': 51049775,
        'download_count': 0,
        'created_at': '2017-09-07T09:24:53Z',
        'updated_at': '2017-09-07T09:24:58Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1-dev.1/tidepool-uploader-2.0.1-dev.1.dmg'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4765429',
        'id': 4765429,
        'name': 'tidepool-uploader-dev-2.0.1-dev.1-mac.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 49067740,
        'download_count': 0,
        'created_at': '2017-09-07T09:23:00Z',
        'updated_at': '2017-09-07T09:23:03Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1-dev.1/tidepool-uploader-dev-2.0.1-dev.1-mac.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4765424',
        'id': 4765424,
        'name': 'tidepool-uploader-dev-2.0.1-dev.1.dmg',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/x-apple-diskimage',
        'state': 'uploaded',
        'size': 51024377,
        'download_count': 0,
        'created_at': '2017-09-07T09:22:24Z',
        'updated_at': '2017-09-07T09:22:31Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1-dev.1/tidepool-uploader-dev-2.0.1-dev.1.dmg'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4765467',
        'id': 4765467,
        'name': 'tidepool-uploader-dev.-2.0.1-dev.1-ia32-win.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 61421199,
        'download_count': 0,
        'created_at': '2017-09-07T09:25:58Z',
        'updated_at': '2017-09-07T09:26:01Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1-dev.1/tidepool-uploader-dev.-2.0.1-dev.1-ia32-win.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4765513',
        'id': 4765513,
        'name': 'tidepool-uploader-dev.-2.0.1-dev.1-win.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 70438513,
        'download_count': 0,
        'created_at': '2017-09-07T09:29:37Z',
        'updated_at': '2017-09-07T09:29:40Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1-dev.1/tidepool-uploader-dev.-2.0.1-dev.1-win.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4765504',
        'id': 4765504,
        'name': 'tidepool-uploader-dev.-setup-2.0.1-dev.1.exe',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/x-msdownload',
        'state': 'uploaded',
        'size': 85673408,
        'download_count': 0,
        'created_at': '2017-09-07T09:29:08Z',
        'updated_at': '2017-09-07T09:29:13Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1-dev.1/tidepool-uploader-dev.-setup-2.0.1-dev.1.exe'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4765557',
        'id': 4765557,
        'name': 'tidepool-uploader-setup-2.0.1-dev.1.exe',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/x-msdownload',
        'state': 'uploaded',
        'size': 85674432,
        'download_count': 12,
        'created_at': '2017-09-07T09:36:00Z',
        'updated_at': '2017-09-07T09:36:08Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.1-dev.1/tidepool-uploader-setup-2.0.1-dev.1.exe'
      }
    ],
    'tarball_url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/tarball/v2.0.1-dev.1',
    'zipball_url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/zipball/v2.0.1-dev.1',
    'body': '- Supports multiple cables per device\r\n- Groups Bayer Contour Next and Abbott FreeStyle meters together'
  },
  {
    'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/7656036',
    'assets_url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/7656036/assets',
    'upload_url': 'https://uploads.github.com/repos/tidepool-org/chrome-uploader/releases/7656036/assets{?name,label}',
    'html_url': 'https://github.com/tidepool-org/chrome-uploader/releases/tag/v2.0.0',
    'id': 7656036,
    'tag_name': 'v2.0.0',
    'target_commitish': 'master',
    'name': '2.0.0',
    'draft': false,
    'author': {
      'login': 'tidepool-org-build-uploader',
      'id': 29215410,
      'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
      'gravatar_id': '',
      'url': 'https://api.github.com/users/tidepool-org-build-uploader',
      'html_url': 'https://github.com/tidepool-org-build-uploader',
      'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
      'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
      'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
      'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
      'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
      'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
      'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
      'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
      'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
      'type': 'User',
      'site_admin': false
    },
    'prerelease': false,
    'created_at': '2017-09-06T16:33:05Z',
    'published_at': '2017-09-06T17:34:32Z',
    'assets': [
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4759581',
        'id': 4759581,
        'name': 'latest-mac.json',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/json',
        'state': 'uploaded',
        'size': 187,
        'download_count': 34,
        'created_at': '2017-09-06T16:43:02Z',
        'updated_at': '2017-09-06T16:43:02Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.0/latest-mac.json'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4759580',
        'id': 4759580,
        'name': 'latest-mac.yml',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'text/yaml',
        'state': 'uploaded',
        'size': 287,
        'download_count': 0,
        'created_at': '2017-09-06T16:43:02Z',
        'updated_at': '2017-09-06T16:43:02Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.0/latest-mac.yml'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4759752',
        'id': 4759752,
        'name': 'latest.yml',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'text/yaml',
        'state': 'uploaded',
        'size': 362,
        'download_count': 5,
        'created_at': '2017-09-06T17:08:24Z',
        'updated_at': '2017-09-06T17:08:24Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.0/latest.yml'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4759710',
        'id': 4759710,
        'name': 'tidepool-uploader-2.0.0-ia32-win.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 61424095,
        'download_count': 0,
        'created_at': '2017-09-06T17:04:49Z',
        'updated_at': '2017-09-06T17:04:52Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.0/tidepool-uploader-2.0.0-ia32-win.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4759579',
        'id': 4759579,
        'name': 'tidepool-uploader-2.0.0-mac.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 49069049,
        'download_count': 3,
        'created_at': '2017-09-06T16:43:02Z',
        'updated_at': '2017-09-06T16:43:06Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.0/tidepool-uploader-2.0.0-mac.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4759753',
        'id': 4759753,
        'name': 'tidepool-uploader-2.0.0-win.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 70432418,
        'download_count': 0,
        'created_at': '2017-09-06T17:08:32Z',
        'updated_at': '2017-09-06T17:08:36Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.0/tidepool-uploader-2.0.0-win.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4759576',
        'id': 4759576,
        'name': 'tidepool-uploader-2.0.0.dmg',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/x-apple-diskimage',
        'state': 'uploaded',
        'size': 51051052,
        'download_count': 15,
        'created_at': '2017-09-06T16:42:26Z',
        'updated_at': '2017-09-06T16:42:32Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.0/tidepool-uploader-2.0.0.dmg'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4759564',
        'id': 4759564,
        'name': 'tidepool-uploader-dev-2.0.0-mac.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 49069065,
        'download_count': 0,
        'created_at': '2017-09-06T16:40:28Z',
        'updated_at': '2017-09-06T16:40:31Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.0/tidepool-uploader-dev-2.0.0-mac.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4759562',
        'id': 4759562,
        'name': 'tidepool-uploader-dev-2.0.0.dmg',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/x-apple-diskimage',
        'state': 'uploaded',
        'size': 51025515,
        'download_count': 2,
        'created_at': '2017-09-06T16:39:57Z',
        'updated_at': '2017-09-06T16:39:59Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.0/tidepool-uploader-dev-2.0.0.dmg'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4759344',
        'id': 4759344,
        'name': 'tidepool-uploader-dev.-2.0.0-ia32-win.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 61421218,
        'download_count': 0,
        'created_at': '2017-09-06T16:15:45Z',
        'updated_at': '2017-09-06T16:15:55Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.0/tidepool-uploader-dev.-2.0.0-ia32-win.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4759380',
        'id': 4759380,
        'name': 'tidepool-uploader-dev.-2.0.0-win.zip',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/zip',
        'state': 'uploaded',
        'size': 70439304,
        'download_count': 1,
        'created_at': '2017-09-06T16:19:29Z',
        'updated_at': '2017-09-06T16:19:33Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.0/tidepool-uploader-dev.-2.0.0-win.zip'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4759377',
        'id': 4759377,
        'name': 'tidepool-uploader-dev.-setup-2.0.0.exe',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/x-msdownload',
        'state': 'uploaded',
        'size': 85672624,
        'download_count': 2,
        'created_at': '2017-09-06T16:18:57Z',
        'updated_at': '2017-09-06T16:19:02Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.0/tidepool-uploader-dev.-setup-2.0.0.exe'
      },
      {
        'url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/releases/assets/4759751',
        'id': 4759751,
        'name': 'tidepool-uploader-setup-2.0.0.exe',
        'label': '',
        'uploader': {
          'login': 'tidepool-org-build-uploader',
          'id': 29215410,
          'avatar_url': 'https://avatars2.githubusercontent.com/u/29215410?v=4',
          'gravatar_id': '',
          'url': 'https://api.github.com/users/tidepool-org-build-uploader',
          'html_url': 'https://github.com/tidepool-org-build-uploader',
          'followers_url': 'https://api.github.com/users/tidepool-org-build-uploader/followers',
          'following_url': 'https://api.github.com/users/tidepool-org-build-uploader/following{/other_user}',
          'gists_url': 'https://api.github.com/users/tidepool-org-build-uploader/gists{/gist_id}',
          'starred_url': 'https://api.github.com/users/tidepool-org-build-uploader/starred{/owner}{/repo}',
          'subscriptions_url': 'https://api.github.com/users/tidepool-org-build-uploader/subscriptions',
          'organizations_url': 'https://api.github.com/users/tidepool-org-build-uploader/orgs',
          'repos_url': 'https://api.github.com/users/tidepool-org-build-uploader/repos',
          'events_url': 'https://api.github.com/users/tidepool-org-build-uploader/events{/privacy}',
          'received_events_url': 'https://api.github.com/users/tidepool-org-build-uploader/received_events',
          'type': 'User',
          'site_admin': false
        },
        'content_type': 'application/x-msdownload',
        'state': 'uploaded',
        'size': 85655688,
        'download_count': 9,
        'created_at': '2017-09-06T17:08:23Z',
        'updated_at': '2017-09-06T17:08:33Z',
        'browser_download_url': 'https://github.com/tidepool-org/chrome-uploader/releases/download/v2.0.0/tidepool-uploader-setup-2.0.0.exe'
      }
    ],
    'tarball_url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/tarball/v2.0.0',
    'zipball_url': 'https://api.github.com/repos/tidepool-org/chrome-uploader/zipball/v2.0.0',
    'body': 'In this release the Uploader is moving from a Chrome App to an Electron app, gaining a number of new features along the way.\r\nHighlights include:\r\n* Automatic self-updating\r\n* Integrated driver installers\r\n* Medtronic direct support\r\n'
  }
];

module.exports = releases;
