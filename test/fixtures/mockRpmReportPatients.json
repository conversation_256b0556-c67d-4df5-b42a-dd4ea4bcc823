{"config": {"code": "CPT-99454", "clinicId": "clinicID123", "startDate": "2024-01-01T05:00:00.000Z", "endDate": "2024-01-31T04:59:59.999Z"}, "results": [{"fullName": "<PERSON>", "birthDate": "2000-01-01", "mrn": "123456", "realtimeDays": 17, "hasSufficientData": true}, {"fullName": "<PERSON>", "birthDate": "1988-03-01", "mrn": "423234", "realtimeDays": 0, "hasSufficientData": false}, {"fullName": "<PERSON>", "birthDate": "1993-04-12", "mrn": "994249", "realtimeDays": 16, "hasSufficientData": true}, {"fullName": "<PERSON><PERSON><PERSON>", "birthDate": "1977-11-11", "mrn": "994423", "realtimeDays": 29, "hasSufficientData": true}, {"fullName": "Jimithon Nodata", "birthDate": null, "mrn": null, "realtimeDays": 0, "hasSufficientData": false}, {"fullName": "Flotsam N. Jetsam", "birthDate": "2000-02-29", "mrn": "994234", "realtimeDays": 30, "hasSufficientData": true}]}